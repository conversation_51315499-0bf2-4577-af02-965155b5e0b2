package com.link.riderservice.data.authorization.data.source.remote

import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.serialization.JSON
import com.link.riderservice.data.authorization.domain.entity.Activate
import com.link.riderservice.data.authorization.domain.entity.ActivateParser
import com.link.riderservice.data.authorization.domain.entity.CheckInfo
import com.link.riderservice.data.authorization.domain.entity.CheckInfoParser
import com.link.riderservice.data.authorization.domain.repository.AuthorizeRepository
import com.link.riderservice.data.source.remote.HttpRequest
import com.link.riderservice.data.source.remote.Method
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

internal class AuthorizeRemoteSourceImpl : AuthorizeRepository {
    private suspend inline fun <DTO, DOMAIN_ENTITY> executeRequest(
        baseUrl: String,
        rawParameters: Map<String, String>,
        parser: (JSON) -> DTO,
        mapper: (DTO) -> DOMAIN_ENTITY
    ): Result<DOMAIN_ENTITY> {
        val encodedParameters = rawParameters.mapValues { (_, value) ->
            try {
                URLEncoder.encode(value, StandardCharsets.UTF_8.name())
            } catch (e: Exception) {
                logD(TAG, "Failed to encode parameter: $value", e)
                value
            }
        }

        val request = HttpRequest(
            url = baseUrl,
            method = Method.GET,
            parameters = encodedParameters,
            headers = mapOf(
                "User-Agent" to "HttpRequest"
            )
        )

        val httpResult = request.response()
        if (httpResult.exception != null) {
            logD(
                TAG,
                "Request to $baseUrl failed with exception: ${httpResult.exception!!.message}"
            )
            return Result.failure(httpResult.exception!!)
        }
        if (!httpResult.success) {
            val errorMessage =
                "Request to $baseUrl failed with response code: ${httpResult.connection?.responseCode}"
            logD(TAG, errorMessage)
            return Result.failure(IllegalStateException(errorMessage))
        }

        val responseBody = httpResult.body
        if (responseBody.isNullOrEmpty()) {
            val errorMessage = "Request to $baseUrl succeeded but response body is null or empty"
            logD(TAG, errorMessage)
            return Result.failure(IllegalStateException(errorMessage))
        }

        return try {
            val json = JSON(responseBody)
            val dto = parser(json)
            Result.success(mapper(dto))
        } catch (e: Exception) {
            val parseErrorMessage = "Failed to parse response from $baseUrl. Body: $responseBody"
            logD(TAG, parseErrorMessage)
            Result.failure(IllegalStateException(parseErrorMessage, e))
        }
    }

    override suspend fun requestActivateStatus(
        key: String,
        mac: String,
        time: String,
        sign: String
    ): Result<Activate> {
        val parameters = mapOf(
            "key" to key,
            "mac" to mac,
            "time" to time,
            "sign" to sign
        )

        return executeRequest(
            AUTHORIZE_ACTIVATE_BASE_URL,
            parameters,
            { json -> ActivateParser().parse(json) },
            { dto -> Activate(dto.status, dto.uuid) }
        )
    }

    override suspend fun requestCheckStatus(
        key: String,
        mac: String,
        uuid: String,
        time: String,
        licenseSign: String,
        sign: String
    ): Result<CheckInfo> {
        val parameters = mapOf(
            "key" to key,
            "mac" to mac,
            "UUID" to uuid,
            "time" to time,
            "licenseSign" to licenseSign,
            "sign" to sign
        )
        logD(TAG, "requestCheckStatus: parameters = $parameters")
        return executeRequest(
            AUTHORIZE_CHECK_BASE_URL,
            parameters,
            { json -> CheckInfoParser().parse(json) },
            { dto -> CheckInfo(dto.status) }
        )
    }

    companion object {
        private const val AUTHORIZE_BASE_URL = "http://www.riderlinkcloud.com.cn:8085"
        private const val AUTHORIZE_ACTIVATE_BASE_URL = "$AUTHORIZE_BASE_URL/activate"
        private const val AUTHORIZE_CHECK_BASE_URL = "$AUTHORIZE_BASE_URL/check"
        private val TAG = AuthorizeRemoteSourceImpl::class.java.simpleName
    }

}