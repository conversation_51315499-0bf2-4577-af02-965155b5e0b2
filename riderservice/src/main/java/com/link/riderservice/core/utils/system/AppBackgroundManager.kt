package com.link.riderservice.core.utils.system

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.link.riderservice.core.utils.logging.logD
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @author: w.feng
 * @date: 2025-08-18
 */
internal object AppBackgroundManager : Application.ActivityLifecycleCallbacks {
    private const val TAG = "AppBackgroundManager"

    enum class AppState {
        FOREGROUND,
        BACKGROUND
    }

    interface AppStateListener {
        /**
         * 当应用的前后台状态发生变化时被调用。
         * @param newState 当前最新的状态 (FOREGROUND 或 BACKGROUND)
         */
        fun onAppStateChanged(newState: AppState)
    }

    private var isInitialized = false
    private var startedActivityCount = 0
    private val listeners = CopyOnWriteArrayList<AppStateListener>()

    var currentState: AppState = AppState.BACKGROUND
        private set

    fun init(application: Application) {
        if (isInitialized) {
            return
        }
        isInitialized = true
        application.registerActivityLifecycleCallbacks(this)
        logD(TAG, "AppBackgroundManager initialized")
    }

    fun addListener(listener: AppStateListener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener)
        }
    }

    fun removeListener(listener: AppStateListener) {
        listeners.remove(listener)
    }


    private fun notifyListeners(newState: AppState) {
        if (newState == currentState) {
            return
        }
        currentState = newState
        logD(TAG, "App state changed to $newState")
        listeners.forEach { it.onAppStateChanged(newState) }
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
    override fun onActivityStarted(activity: Activity) {
        if (startedActivityCount == 0) {
            notifyListeners(AppState.FOREGROUND)
        }
        startedActivityCount++
    }

    override fun onActivityResumed(activity: Activity) {}
    override fun onActivityPaused(activity: Activity) {}
    override fun onActivityStopped(activity: Activity) {
        startedActivityCount--
        if (startedActivityCount == 0) {
            notifyListeners(AppState.BACKGROUND)
        }
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
    override fun onActivityDestroyed(activity: Activity) {}

}