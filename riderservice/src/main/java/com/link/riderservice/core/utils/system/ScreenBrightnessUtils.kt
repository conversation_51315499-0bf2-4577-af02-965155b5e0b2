package com.link.riderservice.core.utils.system

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.link.riderservice.core.utils.logging.logD
import java.util.Collections

internal object ScreenBrightnessUtils {
    private const val TAG = "ScreenBrightnessUtils"
    private var isScreenOff = false

    private var screenBroadcastReceiver: ScreenBroadcastReceiver? = null

    private val listeners: MutableList<OnScreenStateUpdateListener> =
        Collections.synchronizedList(ArrayList())

    fun addScreenListener(onScreenStateUpdateListener: OnScreenStateUpdateListener) {
        if (listeners.contains(onScreenStateUpdateListener)) {
            return
        }
        listeners.add(onScreenStateUpdateListener)
    }

    fun onDestroy(context: Context) {
        unregisterScreenBroadcastReceiver(context)
    }

    fun registerScreenBroadcastReceiver(context: Context) {
        if (screenBroadcastReceiver == null) {
            screenBroadcastReceiver = ScreenBroadcastReceiver()
            val intentFilter = IntentFilter().apply {
                addAction("android.intent.action.SCREEN_ON")
                addAction("android.intent.action.SCREEN_OFF")
                addAction("android.intent.action.USER_PRESENT")
            }
            context.registerReceiver(screenBroadcastReceiver, intentFilter)
        }
    }

    fun removeScreenListener(onScreenStateUpdateListener: OnScreenStateUpdateListener) {
        listeners.remove(onScreenStateUpdateListener)
    }


    fun unregisterScreenBroadcastReceiver(context: Context) {
        if (screenBroadcastReceiver != null) {
            context.unregisterReceiver(screenBroadcastReceiver)
            screenBroadcastReceiver = null
        }
    }


    fun whenScreenOff() {
        if (isScreenOff) {
            return
        }
        logD(TAG, "press power key screen off")
        isScreenOff = true
        listeners.forEach {
            it.whenScreenOff()
        }
    }

    fun whenScreenOn() {
        if (isScreenOff) {
            logD(TAG, "press power key screen on")
            isScreenOff = false
        }
        listeners.forEach {
            it.whenScreenOn()
        }
    }

    fun whenUserPresent() {
        logD(TAG, "press power key user present")
        listeners.forEach {
            it.whenUserPresent()
        }
    }

    fun isScreenOff(): Boolean {
        return isScreenOff
    }

    interface OnScreenStateUpdateListener {
        fun whenScreenOff()
        fun whenScreenOn()
        fun whenUserPresent()
    }

    class ScreenBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            when (intent.action) {
                "android.intent.action.SCREEN_ON" -> {
                    whenScreenOn()
                }

                "android.intent.action.SCREEN_OFF" -> {
                    whenScreenOff()
                }

                "android.intent.action.USER_PRESENT" -> {
                    whenUserPresent()
                }
            }
        }
    }
}