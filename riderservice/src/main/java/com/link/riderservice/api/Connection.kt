package com.link.riderservice.api

import android.bluetooth.BluetoothDevice

/**
 * 连接状态数据类，用于统一管理设备的蓝牙、WiFi和Socket连接状态
 * 
 * 采用不可变数据类设计，确保状态的一致性和线程安全
 * 
 * @see BleStatus
 * @see WifiStatus  
 * @see SocketStatus
 */


/**
 * 连接状态数据类，包含设备的三种连接状态
 * 
 * @param wifiStatus WiFi连接状态，默认为[WifiStatus.IDLE]
 * @param btStatus 蓝牙连接状态，默认为[BleStatus.IDLE]  
 * @param socketStatus Socket连接状态，默认为[SocketStatus.IDLE]
 *
 */
data class Connection(
    val wifiStatus: WifiStatus = WifiStatus.IDLE,
    val btStatus: BleStatus = BleStatus.IDLE,
    val socketStatus: SocketStatus = SocketStatus.IDLE
)

/**
 * 蓝牙连接状态密封类，表示蓝牙设备的各种连接状态
 * 
 * 使用密封类确保所有可能的状态都被明确处理，适用于when表达式中的穷尽检查
 * 
 * @see Connection
 */
sealed class BleStatus {
    /**
     * 空闲状态，未进行任何蓝牙连接操作
     */
    object IDLE : BleStatus()
    
    /**
     * 设备连接中状态
     * 
     * @param device 正在连接的蓝牙设备
     */
    data class DeviceConnecting(val device: BluetoothDevice) : BleStatus()
    
    /**
     * 设备已连接状态
     * 
     * @param device 已连接的蓝牙设备
     */
    data class DeviceConnected(val device: BluetoothDevice) : BleStatus()
    
    /**
     * 设备连接失败状态
     * 
     * @param device 连接失败的蓝牙设备
     * @param reason 连接失败的原因代码
     */
    data class DeviceFailedToConnect(val device: BluetoothDevice, val reason: Int) : BleStatus()
    
    /**
     * 设备断开连接状态
     * 
     * @param device 断开连接的蓝牙设备
     * @param reason 断开连接的原因代码
     */
    data class DeviceDisconnected(val device: BluetoothDevice, val reason: Int) : BleStatus()
}


/**
 * WiFi连接状态密封类，表示WiFi连接的各种状态
 * 
 * 相比蓝牙状态更简单，主要关注连接和断开状态
 * 
 * @see Connection
 */
sealed class WifiStatus {
    /**
     * 空闲状态，未进行任何WiFi连接操作
     */
    object IDLE : WifiStatus()
    
    /**
     * 设备已连接状态，WiFi连接建立成功
     */
    object DeviceConnected : WifiStatus()
    
    /**
     * 设备断开连接状态，WiFi连接已断开
     */
    object DeviceDisconnected : WifiStatus()
}

/**
 * Socket连接状态密封类，表示网络Socket连接的各种状态
 * 
 * 用于管理TCP/UDP等网络Socket的连接状态
 * 
 * @see Connection
 */
sealed class SocketStatus {
    /**
     * 空闲状态，未建立任何Socket连接
     */
    object IDLE : SocketStatus()
    
    /**
     * 已连接状态，Socket连接建立成功
     */
    object Connected : SocketStatus()
    
    /**
     * 断开连接状态，Socket连接已断开
     */
    object Disconnected : SocketStatus()
}