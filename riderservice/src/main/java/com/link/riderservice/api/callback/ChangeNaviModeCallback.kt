package com.link.riderservice.api.callback

import com.link.riderservice.api.SPNaviMode

/**
 * 导航模式变更回调接口
 * 
 * 用于接收导航模式切换事件和状态变化通知
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
interface ChangeNaviModeCallback {
    
    /**
     * 导航模式准备状态变化回调方法
     * 
     * 当调用 [com.link.riderservice.api.SPRiderServices.startNaviMode] 或
     * [com.link.riderservice.api.SPRiderServices.changeNaviMode] 请求切换导航模式时，
     * 设备响应后会触发此回调通知模式准备状态
     * 
     * @param naviMode 请求的导航模式
     * @param isReady 模式是否准备就绪，true-准备就绪，false-准备失败或设备不支持
     * @see com.link.riderservice.api.SPNaviMode
     */
    fun onNaviModeChange(naviMode: SPNaviMode, isReady: Boolean)
    
    /**
     * 导航模式状态变化通知回调方法
     * 
     * 当导航模式实际发生变化时触发，包括：
     * - 用户手动切换模式
     * - 设备自动切换模式
     * - 系统强制切换模式
     * 
     * @param oldMode 切换前的导航模式
     * @param newMode 切换后的导航模式
     * @see com.link.riderservice.api.SPRiderServices.addChangeNaviModeCallback
     * @see com.link.riderservice.api.SPRiderServices.removeChangeNaviModeCallback
     */
    fun onNaviModeChanged(oldMode: SPNaviMode, newMode: SPNaviMode) {
        // 提供默认实现，避免破坏现有代码
    }
}