package com.link.riderservice.api.callback

import com.link.riderservice.api.exception.SPRiderServicesException

/**
 * 异常通知回调接口
 * 
 * 用于接收SDK运行时异常通知，包括连接异常、协议错误、设备兼容性问题等
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
interface SPRiderServicesExceptionNotiCallback {
    
    /**
     * SDK异常通知回调方法
     * 
     * 当SDK发生运行时异常时触发，包括：
     * - 连接异常（蓝牙连接失败、WiFi连接失败等）
     * - 协议错误（消息解析失败、协议版本不兼容等）
     * - 设备兼容性问题（设备不支持特定功能等）
     * - 系统权限问题（缺少必要权限等）
     * - 其他运行时错误
     * 
     * @param exception SDK异常对象，包含详细的错误信息和错误码
     * @see com.link.riderservice.api.exception.SPRiderServicesException
     * @see com.link.riderservice.api.SPRiderServices.addRiderServicesExceptionNotiCallback
     * @see com.link.riderservice.api.SPRiderServices.removeRiderServicesExceptionNotiCallback
     */
    fun onSPRiderServicesExceptionNoti(exception: SPRiderServicesException) {}
}