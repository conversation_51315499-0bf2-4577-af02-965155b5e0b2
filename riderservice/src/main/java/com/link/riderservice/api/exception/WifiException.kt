package com.link.riderservice.api.exception

/**
 * WiFi异常类
 * 
 * 处理WiFi连接和认证相关的异常情况
 * 
 * @see SPRiderServicesException
 */
sealed class WifiException(code: Int, message: String? = null, cause: Throwable? = null) :
    SPRiderServicesException(code, message, cause) {

    /**
     * WiFi未启用异常
     * 
     * @param cause 原始异常
     */
    class WifiDisabled(cause: Throwable? = null) :
        WifiException(1101, "WiFi未启用", cause)

    /**
     * WiFi连接失败异常
     * 
     * @param ssid WiFi网络SSID
     * @param cause 原始异常
     */
    class ConnectionFailed(ssid: String, cause: Throwable? = null) :
        WifiException(1102, "WiFi连接失败: $ssid", cause)

    /**
     * 认证失败异常
     * 
     * @param ssid WiFi网络SSID
     * @param cause 原始异常
     */
    class AuthenticationFailed(ssid: String, cause: Throwable? = null) :
        WifiException(1103, "认证失败: $ssid", cause)

    /**
     * 网络不可用异常
     * 
     * @param cause 原始异常
     */
    class NetworkUnavailable(cause: Throwable? = null) :
        WifiException(1104, "网络不可用", cause)
}