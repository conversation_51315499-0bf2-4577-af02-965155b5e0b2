package com.link.riderservice.api.exception

/**
 * 协议异常类
 * 
 * 处理消息协议解析和验证相关的异常情况
 * 
 * @see SPRiderServicesException
 */
sealed class ProtocolException(code: Int, message: String? = null, cause: Throwable? = null) :
    SPRiderServicesException(code, message, cause) {

    /**
     * 无效消息格式异常
     * 
     * @param format 无效的消息格式
     * @param cause 原始异常
     */
    class InvalidMessage(format: String, cause: Throwable? = null) :
        ProtocolException(2001, "无效消息格式: $format", cause)

    /**
     * 消息解析失败异常
     * 
     * @param messageType 消息类型
     * @param cause 原始异常
     */
    class MessageParsingFailed(messageType: String, cause: Throwable? = null) :
        ProtocolException(2002, "消息解析失败: $messageType", cause)

    /**
     * 版本不匹配异常
     * 
     * @param expected 期望的版本
     * @param actual 实际的版本
     * @param cause 原始异常
     */
    class VersionMismatch(expected: String, actual: String, cause: Throwable? = null) :
        ProtocolException(2003, "版本不匹配: 期望 $expected, 实际 $actual", cause)

    /**
     * 校验和失败异常
     * 
     * @param cause 原始异常
     */
    class ChecksumFailed(cause: Throwable? = null) :
        ProtocolException(2004, "校验和失败", cause)
}