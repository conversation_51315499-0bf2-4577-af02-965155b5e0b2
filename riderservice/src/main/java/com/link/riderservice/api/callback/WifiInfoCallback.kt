package com.link.riderservice.api.callback

/**
 * WiFi信息更新回调接口
 * 
 * 用于接收WiFi连接信息变化通知，包括SSID、信号强度、连接状态等信息
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
interface WifiInfoCallback {
    
    /**
     * WiFi信息接收回调方法
     * 
     * 当WiFi连接信息发生变化时触发，包括：
     * - WiFi连接状态变化（连接成功、断开、连接失败等）
     * - 信号强度变化
     * - SSID信息更新
     * - IP地址信息变化
     * 
     * @param wifiInfo 当前的WiFi信息对象，包含连接模式、SSID、密码等信息
     * @see com.link.riderservice.api.SPWifiInfo
     * @see com.link.riderservice.api.SPRiderServices.addWifiInfoCallback
     * @see com.link.riderservice.api.SPRiderServices.removeWifiInfoCallback
     * @see com.link.riderservice.api.SPRiderServices.getWifiInfo
     * @see com.link.riderservice.api.SPRiderServices.connectWifi
     */
    fun onWifiInfoReceived(wifiInfo: com.link.riderservice.api.SPWifiInfo)
}