package com.link.riderservice.api.callback

import com.link.riderservice.api.BleStatus

/**
 * 蓝牙连接状态回调接口
 * 
 * 用于接收蓝牙连接状态变化事件，包括扫描状态和连接状态变化
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
interface BleStateCallback {
    
    /**
     * 蓝牙扫描开始回调方法
     * 
     * 当调用 [com.link.riderservice.api.SPRiderServices.startScan] 或
     * [com.link.riderservice.api.SPRiderServices.startBleQuickConnect] 开始扫描时触发
     */
    fun onScanning() {}
    
    /**
     * 蓝牙扫描结束回调方法
     * 
     * 当调用 [com.link.riderservice.api.SPRiderServices.stopScan] 停止扫描或
     * 扫描超时自动结束时触发
     */
    fun onScanFinish() {}
    
    /**
     * 蓝牙连接状态变化回调方法
     * 
     * 当蓝牙连接状态发生变化时触发，包括：
     * - 连接成功
     * - 连接断开
     * - 连接失败
     * - 其他连接状态变化
     * 
     * @param status 当前的蓝牙连接状态
     * @see com.link.riderservice.api.BleStatus
     * @see com.link.riderservice.api.SPRiderServices.addBleConnectStateCallback
     * @see com.link.riderservice.api.SPRiderServices.removeBleConnectStateCallback
     */
    fun onBleConnectStateChange(status: BleStatus)
}