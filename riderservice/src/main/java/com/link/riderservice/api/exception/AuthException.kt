package com.link.riderservice.api.exception

/**
 * 认证授权异常类
 * 
 * 处理认证和授权相关的异常情况
 * 
 * @see SPRiderServicesException
 */
sealed class AuthException(code: Int, message: String? = null, cause: Throwable? = null) :
    SPRiderServicesException(code, message, cause) {

    /**
     * 授权失败异常
     * 
     * @param reason 授权失败的具体原因
     * @param cause 原始异常
     */
    class AuthorizationFailed(reason: String, cause: Throwable? = null) :
        AuthException(3001, "授权失败: $reason", cause)

    /**
     * 激活失败异常
     * 
     * @param reason 激活失败的具体原因
     * @param cause 原始异常
     */
    class ActivationFailed(reason: String, cause: Throwable? = null) :
        AuthException(3002, "激活失败: $reason", cause)

    /**
     * 无效凭证异常
     * 
     * @param cause 原始异常
     */
    class InvalidCredentials(cause: Throwable? = null) :
        AuthException(3003, "无效凭证", cause)
}