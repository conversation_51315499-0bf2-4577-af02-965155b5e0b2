package com.link.riderservice.api.callback

import android.view.Display

/**
 * 投屏演示回调接口
 * 
 * 用于接收投屏相关事件通知，包括显示准备、释放和权限需求
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
interface PresentationCallback {
    
    /**
     * 投屏显示准备就绪回调方法
     * 
     * 当投屏显示准备就绪时触发，应用可以开始进行屏幕内容渲染
     * 
     * @param display 可用的显示设备对象，用于投屏内容渲染
     * @see android.view.Display
     */
    fun onPresentationDisplayReady(display: Display) {}
    
    /**
     * 投屏显示释放回调方法
     * 
     * 当投屏显示被释放或不可用时触发，应用应该停止屏幕内容渲染
     * 
     * @param display 被释放的显示设备对象
     */
    fun onPresentationDisplayReleased(display: Display) {}
    
    /**
     * 需要MediaProjection权限回调方法
     * 
     * 当SDK需要屏幕录制权限时触发，用于镜像模式功能
     * 应用需要申请 [android.Manifest.permission.MEDIA_PROJECTION] 权限
     * 并通过 [com.link.riderservice.api.SPRiderServices.setMediaProjection] 设置MediaProjection实例
     * 
     * @see com.link.riderservice.api.SPRiderServices.addPresentationCallback
     * @see com.link.riderservice.api.SPRiderServices.removePresentationCallback
     * @see com.link.riderservice.api.SPRiderServices.setMediaProjection
     */
    fun onMediaProjectionPermissionRequired() {}
}