package com.link.riderservice.api.callback

/**
 * 通用数据接收回调接口
 * 
 * 用于接收设备发送的通用二进制数据，支持扩展功能和自定义协议
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
interface ReceiveCommonDataCallback {
    
    /**
     * 通用数据接收回调方法
     * 
     * 当设备发送通用二进制数据时触发，用于：
     * - 扩展功能数据传输
     * - 自定义协议实现
     * - 设备到应用的非标准数据通信
     * 
     * 数据格式由应用层和设备端协商确定，SDK不进行解析处理
     * 
     * @param data 接收到的二进制数据字节数组
     * @see com.link.riderservice.api.SPRiderServices.addReceiveCommonDataCallback
     * @see com.link.riderservice.api.SPRiderServices.removeReceiveCommonDataCallback
     * @see com.link.riderservice.api.SPRiderServices.sendCommonData
     */
    fun onReceiveCommonData(data: ByteArray)
}