package com.link.riderservice.api.exception

/**
 * 设备异常类
 * 
 * 处理设备功能和状态相关的异常情况
 * 
 * @see SPRiderServicesException
 */
sealed class DeviceException(code: Int, message: String? = null, cause: Throwable? = null) :
    SPRiderServicesException(code, message, cause) {

    /**
     * 功能不支持异常
     * 
     * @param feature 不支持的功能名称
     * @param cause 原始异常
     */
    class FeatureNotSupported(feature: String, cause: Throwable? = null) :
        DeviceException(4001, "功能不支持: $feature", cause)

    /**
     * 设备繁忙异常
     * 
     * @param cause 原始异常
     */
    class DeviceBusy(cause: Throwable? = null) :
        DeviceException(4002, "设备繁忙", cause)

    /**
     * 电量低异常
     * 
     * @param cause 原始异常
     */
    class LowBattery(cause: Throwable? = null) :
        DeviceException(4003, "电量低", cause)

    /**
     * 存储空间不足异常
     * 
     * @param cause 原始异常
     */
    class MemoryFull(cause: Throwable? = null) :
        DeviceException(4004, "存储空间不足", cause)
}