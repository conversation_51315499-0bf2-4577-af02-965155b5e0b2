package com.link.riderservice.api.exception

/**
 * 网络异常类
 * 
 * 处理网络连接和通信相关的异常情况
 * 
 * @see SPRiderServicesException
 */
sealed class NetworkException(code: Int, message: String? = null, cause: Throwable? = null) :
    SPRiderServicesException(code, message, cause) {
    /**
     * 连接超时异常
     * 
     * @param cause 原始异常
     */
    class ConnectionTimeout(cause: Throwable? = null) :
        NetworkException(1201, "连接超时", cause)

    /**
     * 网络错误异常
     * 
     * @param cause 原始异常
     */
    class NetworkError(cause: Throwable? = null) :
        NetworkException(1202, "网络错误", cause)
}