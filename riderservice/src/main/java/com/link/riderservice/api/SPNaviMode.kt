package com.link.riderservice.api

/**
 * 导航模式枚举
 * 
 * 定义不同的导航显示和工作模式
 */
enum class SPNaviMode {
    /**
     * 简易导航模式
     */
    SPNaviModeSimpleNavi,

    /**
     * 投屏导航模式
     */
    SPNaviModeScreenNavi,

    /**
     * 巡航导航模式
     */
    SPNaviModeCruiseNavi,

    /**
     * 镜像导航模式
     */
    SPNaviModeMirrorNavi,

    /**
     * 默认导航模式
     */
    SPNaviModeDefaultNavi,

    /**
     * 锁屏导航模式
     */
    SPNaviModeLockScreenNavi,

    /**
     * 无导航模式
     */
    SPNaviModeNoNavi,
}