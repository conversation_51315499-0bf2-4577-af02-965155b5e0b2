package com.link.riderservice.api.exception

/**
 * 蓝牙异常类
 * 
 * 处理蓝牙连接和设备相关的异常情况
 * 
 * @see SPRiderServicesException
 */
sealed class BluetoothException(code: Int, message: String? = null, cause: Throwable? = null) :
    SPRiderServicesException(code, message, cause) {
    /**
     * 蓝牙未启用异常
     * 
     * @param cause 原始异常
     */
    class BluetoothDisabled(cause: Throwable? = null) :
        BluetoothException(1001, "蓝牙未启用", cause)

    /**
     * 设备未找到异常
     * 
     * @param deviceName 设备名称
     * @param cause 原始异常
     */
    class DeviceNotFound(deviceName: String, cause: Throwable? = null) :
        BluetoothException(1002, "设备未找到: $deviceName", cause)

    /**
     * 连接失败异常
     * 
     * @param deviceAddress 设备地址
     * @param cause 原始异常
     */
    class ConnectionFailed(deviceAddress: String, cause: Throwable? = null) :
        BluetoothException(1003, "连接失败: $deviceAddress", cause)

    /**
     * 设备断开连接异常
     * 
     * @param deviceName 设备名称
     * @param cause 原始异常
     */
    class DeviceDisconnected(deviceName: String, cause: Throwable? = null) :
        BluetoothException(1004, "设备断开连接: $deviceName", cause)

    /**
     * 配对失败异常
     * 
     * @param deviceName 设备名称
     * @param cause 原始异常
     */
    class PairingFailed(deviceName: String, cause: Throwable? = null) :
        BluetoothException(1005, "配对失败: $deviceName", cause)
}