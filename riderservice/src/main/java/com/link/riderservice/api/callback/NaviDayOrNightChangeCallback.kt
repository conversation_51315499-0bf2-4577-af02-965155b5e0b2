package com.link.riderservice.api.callback

import com.link.riderservice.api.SPNaviDayOrNight

/**
 * 导航日夜模式变更回调接口
 * 
 * 用于接收导航日夜模式切换事件通知
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
interface NaviDayOrNightChangeCallback {
    
    /**
     * 导航日夜模式变更回调方法
     * 
     * 当导航日夜模式发生变化时触发，包括：
     * - 设备根据环境光线自动切换日夜模式
     * - 用户手动切换日夜模式
     * - 系统根据时间自动切换日夜模式
     * 
     * @param naviTheme 切换后的导航主题模式（日间模式或夜间模式）
     * @see com.link.riderservice.api.SPNaviDayOrNight
     * @see com.link.riderservice.api.SPRiderServices.addNaviDayOrNightChangeCallback
     * @see com.link.riderservice.api.SPRiderServices.removeNaviDayOrNightChangeCallback
     */
    fun onNviDayOrNightChange(naviTheme: SPNaviDayOrNight)
}