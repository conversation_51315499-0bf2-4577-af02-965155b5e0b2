package com.link.riderservice.api.dto

/**
 * 仪表盘配置
 *
 * @property isSupportDvr 是否支持DVR
 * @property isSupportNavi 是否支持导航
 * @property isSupportScreenNavi 是否支持投屏导航
 * @property isSupportWeather 是否支持天气
 * @property isSupportNotification 是否支持通知
 * @property isSupportCircularScreen 是否支持圆屏
 * @property isSupportCruise 是否支持巡航
 * @property isSupportMirror 是否支持镜像
 */
data class DeviceConfig(
    val isSupportDvr: <PERSON>olean,
    val isSupportNavi: Boolean,
    val isSupportScreenNavi: <PERSON><PERSON>an,
    val isSupportWeather: <PERSON>olean,
    val isSupportNotification: <PERSON><PERSON>an,
    val isSupportCircularScreen: <PERSON>olean,
    val isSupportCruise:<PERSON>olean,
    val isSupportMirror:Boolean
)
