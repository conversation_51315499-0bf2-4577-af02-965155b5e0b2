package com.link.riderservice.api.callback

/**
 * 权限检测回调接口
 * 
 * 用于接收权限需求通知，当SDK需要特定系统权限时触发
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
interface PermissionDetectionCallback {
    
    /**
     * 需要蓝牙扫描权限回调方法
     * 
     * 当SDK需要蓝牙扫描权限时触发，通常在以下场景：
     * - 调用 [com.link.riderservice.api.SPRiderServices.startScan] 开始扫描设备
     * - 调用 [com.link.riderservice.api.SPRiderServices.startBleQuickConnect] 快速连接
     * - 其他需要蓝牙扫描功能的操作
     * 
     * 应用需要申请 [android.Manifest.permission.BLUETOOTH_SCAN] 权限
     */
    fun onNeedBluetoothScanPermission()
    
    /**
     * 需要位置权限回调方法
     * 
     * 当SDK需要位置权限时触发，通常在以下场景：
     * - Android 6.0+ 系统需要位置权限才能进行蓝牙扫描
     * - 需要获取设备位置信息用于导航功能
     * - 其他需要位置访问权限的操作
     * 
     * 应用需要申请 [android.Manifest.permission.ACCESS_FINE_LOCATION] 或
     * [android.Manifest.permission.ACCESS_COARSE_LOCATION] 权限
     * 
     * @see com.link.riderservice.api.SPRiderServices.addPermissionDetectionCallback
     * @see com.link.riderservice.api.SPRiderServices.removePermissionDetectionCallback
     */
    fun onNeedLocationPermission()
}