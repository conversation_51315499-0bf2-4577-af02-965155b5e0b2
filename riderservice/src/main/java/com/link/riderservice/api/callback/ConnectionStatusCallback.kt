package com.link.riderservice.api.callback

import com.link.riderservice.api.Connection

/**
 * 完整连接状态回调接口
 * 
 * 用于接收完整的连接状态变化通知，包含蓝牙和WiFi等所有连接组件的状态信息
 * 
 * <AUTHOR>
 * @since 2025-08-15
 */
interface ConnectionStatusCallback {
    
    /**
     * 连接状态变化回调方法
     * 
     * 当整体连接状态发生变化时触发，包括：
     * - 蓝牙连接状态变化
     * - WiFi连接状态变化  
     * - 设备连接状态变化
     * - 其他连接相关状态变化
     * 
     * @param status 完整的连接状态对象，包含所有连接相关信息
     * @see com.link.riderservice.api.Connection
     * @see com.link.riderservice.api.SPRiderServices.addConnectionStatusCallback
     * @see com.link.riderservice.api.SPRiderServices.removeConnectionStatusCallback
     * @see com.link.riderservice.api.SPRiderServices.getConnectStatus
     */
    fun onConnectionStatusChanged(status: Connection)
}