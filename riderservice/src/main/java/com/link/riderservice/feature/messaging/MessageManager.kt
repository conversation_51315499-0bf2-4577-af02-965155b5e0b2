package com.link.riderservice.feature.messaging

import com.google.protobuf.MessageLite
import com.link.riderservice.api.SPNaviMode
import com.link.riderservice.api.dto.ArriveDestination
import com.link.riderservice.api.dto.GpsSignalWeak
import com.link.riderservice.api.dto.NaviInfoNotification
import com.link.riderservice.api.dto.NaviInitSuccess
import com.link.riderservice.api.dto.NaviText
import com.link.riderservice.api.dto.NaviVersionRequest
import com.link.riderservice.api.dto.NotificationInfo
import com.link.riderservice.api.dto.RiderMessage
import com.link.riderservice.api.dto.SPWeatherInfo
import com.link.riderservice.core.extensions.collection.readInt16BE
import com.link.riderservice.core.extensions.collection.readInt32BE
import com.link.riderservice.core.extensions.collection.readUInt8
import com.link.riderservice.core.extensions.collection.writeInt16BE
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logW
import com.link.riderservice.core.utils.system.TimeUtils
import com.link.riderservice.feature.connection.mode.AutoLinkConnect
import com.link.riderservice.protobuf.RiderProtocol
import java.lang.ref.WeakReference
import java.nio.ByteBuffer
import java.util.concurrent.ConcurrentLinkedDeque

/**
 * <AUTHOR>
 * @date 2022/6/29
 */
internal object MessageManager {
    private const val TAG = "MessageManager"
    private const val MAX_PAYLOAD_SIZE = 90
    private const val BITFIELD_LENGTH = 1
    private const val FRAME_LENGTH = 2
    private const val MESSAGE_LENGTH = 4
    private const val MESSAGE_ID_LENGTH = 2
    private const val MESSAGE_TYPE_INVALID = -1
    private val incomingMessages = ConcurrentLinkedDeque<NaviMessage>()
    private val outgoingMessages = ConcurrentLinkedDeque<NaviMessage>()
    private var messageCallbackRef: WeakReference<MessageCallback>? = null

    /**
     * 注册回调
     */
    internal fun registerCallback(callback: MessageCallback) {
        messageCallbackRef = WeakReference(callback)
    }

    /**
     * 注销回调
     */
    internal fun unregisterCallback() {
        messageCallbackRef = null
    }

    /**
     * 出队发送数据
     */
    internal fun dequeueOutgoing(): NaviMessage? {
        return outgoingMessages.poll()
    }

    /**
     * 入队发送数据
     */
    internal fun queueOutgoing(messageId: Int, messageProto: MessageLite) {
        val dataLength = messageProto.serializedSize
        val byteBuffer = ByteBuffer.allocate(dataLength + MESSAGE_ID_LENGTH)
        val data = byteBuffer.array()
        data.writeInt16BE(messageId)
        System.arraycopy(messageProto.toByteArray(), 0, data, MESSAGE_ID_LENGTH, dataLength)
        messageToFrames(data, data.size)
    }

    /**
     * 入队接收到的数据
     */
    internal fun enqueueIncoming(buffer: ByteArray, size: Int) {
        decodeFrame(ByteBuffer.wrap(buffer, 0, size))
    }

    private fun enqueueIncoming(message: NaviMessage) {
        incomingMessages.addLast(message)
    }

    /**
     * 清除所有缓存信息
     */
    internal fun clearMessage() {
        outgoingMessages.clear()
        incomingMessages.clear()
    }

    private fun peekLastIncoming(): NaviMessage? {
        return incomingMessages.peekLast()
    }

    private fun hasIncoming(): Boolean {
        return !incomingMessages.isEmpty()
    }

    private fun dequeueIncoming(): NaviMessage? {
        return incomingMessages.poll()
    }

    private fun hasOutgoing(): Boolean {
        return !outgoingMessages.isEmpty()
    }

    private fun enqueueOutgoing(message: NaviMessage) {
        outgoingMessages.addLast(message)
    }

    private fun messageToFrames(data: ByteArray, dataLength: Int) {
        val message = ByteBuffer.wrap(data)
        var (fragInfo, fragLen) = calculateFragInfoAndLen(dataLength)
        var remaining = dataLength
        var offset = 0
        while (remaining > 0) {
            val payload = ByteArray(fragLen)
            message.position(offset)
            message.get(payload)
            val naviMessage = NaviMessage(
                header = fragInfo,
                messagePayload = payload,
                frameLength = fragLen,
                messageLength = if (fragInfo == RiderProtocol.FragInfo.FRAG_FIRST_VALUE) dataLength else 0
            )
            offset += fragLen
            remaining -= fragLen
            val (newFragInfo, newFragLen) = calculateFragInfoAndLen(remaining)
            fragInfo = newFragInfo
            fragLen = newFragLen
            enqueueOutgoing(naviMessage)
        }
    }

    private fun calculateFragInfoAndLen(dataLength: Int): Pair<Int, Int> {
        return if (dataLength > MAX_PAYLOAD_SIZE) {
            RiderProtocol.FragInfo.FRAG_FIRST_VALUE to MAX_PAYLOAD_SIZE
        } else {
            RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE to dataLength
        }
    }

    private fun framesToMessage(): ByteArray? {
        var frame = dequeueIncoming()
        if (frame?.header == RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE) {
            return frame.messagePayload
        }
        var offset = 0
        if (frame != null) {
            val messageData = ByteArray(frame.messageLength)
            while (true) {
                frame?.let { data ->
                    data.messagePayload?.let { payload ->
                        System.arraycopy(payload, 0, messageData, offset, data.frameLength)
                    }
                    offset += data.frameLength
                }
                if (!hasIncoming()) {
                    break
                }
                frame = dequeueIncoming()
            }
            return messageData
        }
        return null
    }

    private fun decodeFrame(buffer: ByteBuffer) {
        val bitField = buffer.array().readUInt8()
        val frameLength = buffer.array().readInt16BE(BITFIELD_LENGTH)
        val messageLength =
            if (bitField == RiderProtocol.FragInfo.FRAG_FIRST_VALUE) buffer.array()
                .readInt32BE(FRAME_LENGTH + BITFIELD_LENGTH) else 0
        val offset = if (messageLength == 0)
            FRAME_LENGTH + BITFIELD_LENGTH
        else
            FRAME_LENGTH + BITFIELD_LENGTH + MESSAGE_LENGTH
        val size = buffer.limit() - offset
        val payload = ByteArray(size)
        buffer.position(offset)
        buffer.get(payload)
        val frame = NaviMessage(
            header = bitField,
            frameLength = frameLength,
            messageLength = messageLength,
            messagePayload = payload,
            originalMessageData = buffer.array()
        )
        val lastFrame = peekLastIncoming()
        val hasIncoming = hasIncoming()
        val valid = isValid(frame, lastFrame, hasIncoming)
        val complete = isComplete(frame, lastFrame, hasIncoming)
        if (!valid) {
            logW(TAG, "invalid frame")
        }
        enqueueIncoming(frame)
        if (complete) {
            val messageData = framesToMessage()
            val type = messageData?.readInt16BE() ?: MESSAGE_TYPE_INVALID
            routeMessage(type, messageData)
        }
    }

    private fun isValid(
        frame: NaviMessage,
        lastFrame: NaviMessage?,
        hasIncoming: Boolean
    ): Boolean {
        return when {
            hasIncoming -> {
                val validFirstOrContinuation =
                    lastFrame?.header == RiderProtocol.FragInfo.FRAG_FIRST_VALUE
                            || lastFrame?.header == RiderProtocol.FragInfo.FRAG_CONTINUATION_VALUE
                val validContinuationOrLast =
                    frame.header == RiderProtocol.FragInfo.FRAG_CONTINUATION_VALUE
                            || frame.header == RiderProtocol.FragInfo.FRAG_LAST_VALUE
                validFirstOrContinuation && validContinuationOrLast
            }

            else -> {
                frame.header == RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE
                        || frame.header == RiderProtocol.FragInfo.FRAG_FIRST_VALUE
            }
        }
    }

    private fun isComplete(
        frame: NaviMessage,
        lastFrame: NaviMessage?,
        hasIncoming: Boolean
    ): Boolean {
        return when {
            hasIncoming -> {
                val validFirstOrContinuation =
                    lastFrame?.header == RiderProtocol.FragInfo.FRAG_FIRST_VALUE
                            || lastFrame?.header == RiderProtocol.FragInfo.FRAG_CONTINUATION_VALUE
                val validLast = frame.header == RiderProtocol.FragInfo.FRAG_LAST_VALUE
                validFirstOrContinuation && validLast
            }

            else -> {
                frame.header == RiderProtocol.FragInfo.FRAG_UNFRAGMENTED_VALUE
            }
        }
    }

    private fun routeMessage(type: Int, messageData: ByteArray?) {
        if (type == -1) {
            return
        }
        val messagePayload = messageData?.copyOfRange(MESSAGE_ID_LENGTH, messageData.size)
        messageCallbackRef?.get()?.onMessage(type, messagePayload)
    }

    internal fun sendMessage(message: RiderMessage) {
        when (message) {
            is NotificationInfo -> {
                handleNotificationInfo(message)
            }

            is SPWeatherInfo -> {
                handleWeatherInfo(message)
            }

            is NaviInfoNotification -> {
                handleNaviInfo(message)
            }

            is ArriveDestination -> {
                handleArriveDestination()
            }

            is GpsSignalWeak -> {
                val id = RiderProtocol.NaviMessageId.MSG_GPS_SIGNAL_WEAK_VALUE
                val protocolMessage =
                    RiderProtocol.GpsSignalWeak.newBuilder().setIsWeak(message.isWeek).build()
                sendMessageToRiderService(id, protocolMessage)
            }

            is NaviInitSuccess -> {
                val id = RiderProtocol.NaviMessageId.MSG_NAVI_INIT_SUCCESS_VALUE
                val protocolMessage = RiderProtocol.NaviInitSuccess.newBuilder().build()
                sendMessageToRiderService(id, protocolMessage)
            }


            is NaviText -> {
                handleNaviText(message)
            }

            is NaviVersionRequest -> {
                handleNaviVersionRequest()
            }

            else -> {}
        }
    }

    internal fun sendMessageNaviStart() {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_START_VALUE
        val protocolMessage = RiderProtocol.NaviStart.newBuilder().build()
        sendMessageToRiderService(id, protocolMessage)
    }

    internal fun sendMessageNaviStop() {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_STOP_VALUE
        val protocolMessage = RiderProtocol.NaviStop.newBuilder().build()
        sendMessageToRiderService(id, protocolMessage)
    }

    internal fun sendMessageNaviRequest(spNaviMode: SPNaviMode) {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_REQUEST_VALUE
        val protocolMessage =
            RiderProtocol.NaviRequest.newBuilder().setNaviMode(convertNaviMode(spNaviMode)).build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun sendMessageToRiderService(id: Int, protocolMessage: MessageLite) {
        queueOutgoing(id, protocolMessage)
    }


    private fun handleNotificationInfo(message: NotificationInfo) {
        val id = RiderProtocol.NaviMessageId.MSG_ANDROID_NOTIFICATION_VALUE
        val protocolMessage = RiderProtocol.AndroidNotification.newBuilder()
            .setAppName(message.appName)
            .setTitle(message.title)
            .setContent(message.content)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleWeatherInfo(message: SPWeatherInfo) {
        val id = RiderProtocol.NaviMessageId.MSG_WEATHER_INFO_NOTIFICATION_VALUE
        val protocolMessage = RiderProtocol.WeatherInfoNotification.newBuilder()
            .setWea(message.wea)
            .setTem(message.tem)
            .setHumidity(message.humidity)
            .setPressure(message.pressure)
            .setAltitude(message.altitude)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleNaviInfo(message: NaviInfoNotification) {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_INFO_VALUE
        val protocolMessage = RiderProtocol.NaviInfoNotification.newBuilder()
            .setCurLink(message.curLink)
            .setCurPoint(message.curPoint)
            .setCurStep(message.curStep)
            .setCurStepRetainDistance(message.curStepRetainDistance)
            .setCurStepRetainTime(message.curStepRetainTime)
            .setNaviType(message.naviType)
            .setPathRetainTime(message.pathRetainTime)
            .setPathRetainDistance(message.pathRetainDistance)
            .setRouteRemainLightCount(message.routeRemainLightCount)
            .setPathId(message.pathId)
            .setNextRoadName(message.nextRoadName)
            .setCurrentRoadName(message.currentRoadName)
            .setIconType(message.iconType)
            .setMapType(message.mapType)
            .setTurnIconName(message.turnIconName)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleArriveDestination() {
        val id = RiderProtocol.NaviMessageId.MSG_ARRIVE_DEST_VALUE
        val protocolMessage = RiderProtocol.ArriveDestination.newBuilder().build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleNaviText(message: NaviText) {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_TEXT_VALUE
        val protocolMessage =
            RiderProtocol.NaviText.newBuilder().setText(message.text).setType(message.type).build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun handleNaviVersionRequest() {
        val id = RiderProtocol.NaviMessageId.MSG_NAVI_VERSION_REQUEST_VALUE
        val protocolMessage = RiderProtocol.NaviVersionRequest.newBuilder()
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    private fun convertNaviMode(spNaviMode: SPNaviMode): RiderProtocol.NaviMode =
        when (spNaviMode) {
            SPNaviMode.SPNaviModeDefaultNavi -> RiderProtocol.NaviMode.DEFAULT_NAVI
            SPNaviMode.SPNaviModeSimpleNavi -> RiderProtocol.NaviMode.SIMPLE_NAVI
            SPNaviMode.SPNaviModeScreenNavi -> RiderProtocol.NaviMode.SCREEN_NAVI
            SPNaviMode.SPNaviModeCruiseNavi -> RiderProtocol.NaviMode.CRUISE_NAVI
            SPNaviMode.SPNaviModeMirrorNavi -> RiderProtocol.NaviMode.MIRROR_NAVI
            SPNaviMode.SPNaviModeLockScreenNavi -> RiderProtocol.NaviMode.LOCK_SCREEN_NAVI
            else -> RiderProtocol.NaviMode.NO_NAVI
        }

    internal fun sendMessageWeatherInfo(weatherInfo: SPWeatherInfo) {
        val id = RiderProtocol.NaviMessageId.MSG_WEATHER_INFO_NOTIFICATION_VALUE
        val protocolMessage = RiderProtocol.WeatherInfoNotification.newBuilder()
            .setWea(weatherInfo.wea)
            .setTem(weatherInfo.tem)
            .setHumidity(weatherInfo.humidity)
            .setPressure(weatherInfo.pressure)
            .setAltitude(weatherInfo.altitude)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }

    internal fun startNaviMode(naviMode: SPNaviMode) {
        val naviModeStart = RiderProtocol.NaviModeStart.newBuilder().apply {
            this.naviMode = convertNaviMode(naviMode)
        }.build()
        queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_NAVI_MODE_START_VALUE, naviModeStart
        )
    }

    internal fun stopNaviMode(naviMode: SPNaviMode) {
        val naviModeStop = RiderProtocol.NaviModeStop.newBuilder().apply {
            this.naviMode = convertNaviMode(naviMode)
        }.build()
        queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_NAVI_MODE_STOP_VALUE, naviModeStop
        )
    }

    internal fun sendTimeInfo() {
        val timeNotification =
            RiderProtocol.TimeNotification.newBuilder().setTime(TimeUtils.getCurrentZeroTimeStr())
                .build()

        queueOutgoing(
            RiderProtocol.NaviMessageId.MSG_TIME_NOTIFICATION_VALUE, timeNotification
        )
    }

    internal fun sendAutoLinkConnect(connect: AutoLinkConnect) {
        val id = RiderProtocol.NaviMessageId.MSG_AUTOLINK_CONNECT_VALUE
        logD(TAG, "IP: " + connect.ip)
        val protocolMessage = RiderProtocol.AutoLinkConnect.newBuilder()
            .setIp(connect.ip)
            .build()
        sendMessageToRiderService(id, protocolMessage)
    }


}