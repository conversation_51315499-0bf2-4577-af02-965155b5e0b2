package com.link.riderservice.feature.display.protocol.project

import android.util.SparseArray
import androidx.core.util.forEach
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.feature.connection.transport.Transport
import com.link.riderservice.feature.display.protocol.source.ALServiceBase
import com.link.riderservice.feature.display.protocol.source.ALTransport
import java.util.Calendar

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal class GalReceiver(
    phoneInfo: PhoneInfo,
    private val appMessageListener: AppMessageListener,
    private val byeByeHandler: ByeByeHandler
) : NativeObject,
    ControllerCallbacks {
    private val registeredServices = SparseArray<ALServiceBase>()
    private var galTransport: ALTransport?
    override val nativeInstance: Long = 0
    private var isStopped = false
    private var huMake: String? = null

    interface AppMessageListener {
        fun onAutoRotationRequest(isAutomaticRotationEnabled: Boolean)
        fun onDisconnected()
        fun onUnrecoverableError(errorCode: Int)
        fun onVersionCallback(major: Short, minor: Short)
        fun onCarInfoCallback(
            id: String?, make: String?, model: String?, year: String?, huIc: String?,
            huMake: String?,
            huModel: String?, huSwBuild: String?, huSwVersion: String?,
            huSeries: String?,
            huMuVersion: String?, checkSum: Int
        )

        fun onScreenOrientationInquire()
        fun onForceLandscapeRequest(isLandscapeModeEnabled: Boolean)
        fun onExitRequest()
        fun onRunningState(state: Protos.State)
        fun onVideoChannelReady()
    }

    interface ByeByeHandler {
        fun onByeByeRequest(reason: Int)
        fun onByeByeResponse()
    }

    override fun destroy() {
        stop()
        destroyCarServices()
        nativeShutdown()
        galTransport = null
    }

    fun start() {
        if (isStopped) {
            throw RuntimeException("can't be started after teardown")
        }
        startCarServices()
        nativeStart()
    }

    fun sendByeByeRequest(reasonCode: Int) {
        nativeSendByeByeRequest(reasonCode)
    }

    fun sendExitResponse() {
        nativeSendExitResponse()
    }

    fun startTransport(transport: Transport?) {
        galTransport?.start(transport)
    }

    /**
     * stop gal.
     */
    fun stop() {
        if (isStopped) {
            return
        }
        isStopped = true
        nativePrepareShutdown()
        galTransport?.stop()
    }

    /**
     * register channel.
     *
     * @param serviceId channel id
     * @param service   channel instance
     */
     fun registerCarService(serviceId: Int, service: ALServiceBase) {
        if (isStopped) {
            throw RuntimeException("can't register services after teardown")
        }
        require(registeredServices[serviceId] == null) { "this service ID is already taken" }
        registeredServices.put(serviceId, service)
        if (!service.create(serviceId, nativeInstance)) {
            logE(TAG,"Service $serviceId failed to init.")
        }
    }

    private fun startCarServices() {
        registeredServices.forEach { serviceId, service ->
            if (!nativeRegister(service.nativeInstance)) {
                logE(TAG,"Service $serviceId failed to register.")
            }
        }
    }

    /**
     * close channel.
     */
    fun destroyCarServices() {
        registeredServices.forEach { _, service ->
            service.destroy()
        }
        registeredServices.clear()
    }

    override fun unrecoverableErrorCallback(err: Int) {
        appMessageListener.onUnrecoverableError(err)
    }

    override fun screenOrientationInquire() {
        appMessageListener.onScreenOrientationInquire()
    }

    override fun pingResponseCallback(timestamp: Long) {}
    override fun pingRequestCallback(timestamp: Long, bugReport: Boolean) {
    }

    override fun navigationFocusCallback(type: Int) {}
    override fun forceLandscapeRequestCallback(force: Boolean) {
        appMessageListener.onForceLandscapeRequest(force)
    }

    override fun byeByeResponseCallback() {
        byeByeHandler.onByeByeResponse()
    }

    override fun exitRequestCallback() {
        appMessageListener.onExitRequest()
    }

    override fun byeByeRequestCallback(reason: Int) {
        byeByeHandler.onByeByeRequest(reason)
    }

    override fun authCompleteCallback() {
        appMessageListener.onRunningState(state = Protos.State.PROBE_SUCCESS)
    }

    override fun audioFocusNotificationCallback(request: Int, unsolicited: Boolean) {}
    override fun versionResponseCallback(major: Short, minor: Short) {
        appMessageListener.onVersionCallback(major, minor)
    }

    override fun serviceDiscoveryResponseCallback(
        id: String?, make: String?, model: String?, year: String?,
        huIc: String?, huMake: String?, huModel: String?,
        huSwBuild: String?, huSwVersion: String?,
        huSeries: String?, huMuVersion: String?,
        checkSum: Int
    ) {
        this.huMake = huMake
        appMessageListener.onCarInfoCallback(
            id,
            make,
            model,
            year,
            huIc,
            huMake,
            huModel,
            huSwBuild,
            huSwVersion,
            huSeries,
            huMuVersion,
            checkSum
        )
    }

    override fun autoRotationRequest(autoed: Boolean) {
        appMessageListener.onAutoRotationRequest(autoed)
    }

    override fun screenResolutionInquire() {
        appMessageListener.onScreenOrientationInquire()
    }

    override fun timeDateInquire() {
        val calendar = Calendar.getInstance()
        sendTimeDateNotification(
            calendar[Calendar.YEAR], calendar[Calendar.MONTH] + 1,
            calendar[Calendar.DAY_OF_MONTH], calendar[Calendar.HOUR_OF_DAY],
            calendar[Calendar.MINUTE], calendar[Calendar.SECOND],
            calendar[Calendar.MILLISECOND] * 1000 * 1000,
            calendar[Calendar.WEEK_OF_YEAR],
            calendar[Calendar.DAY_OF_WEEK]
        )
    }

    private external fun nativeInit()
    private external fun nativeStart()
    private external fun nativePrepareShutdown()
    private external fun nativeShutdown()
    private external fun nativeQueueIncoming(buffer: ByteArray, length: Int): Int
    private external fun nativeGetAdditionalBytesToRead(buffer: ByteArray): Int
    private external fun nativeGetEncodedFrame(buffer: ByteArray): Int
    private external fun nativeRegister(nativeProtocolEndpoint: Long): Boolean
    private external fun nativeSetPhoneInfo(info: PhoneInfo)
    private external fun nativePing(timestamp: Long, bugReport: Boolean)
    private external fun nativeSetNavFocus(type: Int)
    private external fun nativeSendByeByeRequest(reasonCode: Int)
    private external fun nativeSendByeByeResponse()
    private external fun nativeSendExitResponse()
    external fun nativeSendScreenOrientationNotifi(orientation: Int, rotation: Int)
    external fun nativeSendUpdateVehicleIdNotifi(id: String?)
    private external fun nativeSendRunningStateNotifi(state: Int)
    private external fun nativeSendAutoRotationNotifi(isAutomaticRotationEnabled: Boolean)
    external fun nativeSendScreenResolutionNotification(
        width: Int, height: Int,
        isRequired: Boolean
    )

    private external fun sendTimeDateNotification(
        year: Int, month: Int, day: Int, hour: Int, minute: Int,
        second: Int,
        nanosecond: Int, week: Int, dayOfWeek: Int
    )

    companion object {
        private const val TAG = "GalReceiver"
        private const val PING_CHECK_TIMEOUT = 7000
    }

    init {
        val frameParser: ALTransport.FrameParser = object : ALTransport.FrameParser {
            override fun readerTerminated() {
                appMessageListener.onDisconnected()
            }

            override fun onIoError() {
                unrecoverableErrorCallback(-251)
            }

            override fun getFrameSizeToRead(headerBytes: ByteArray, frameSize: Int): Int {
                return nativeGetAdditionalBytesToRead(headerBytes)
            }

            override fun enqueueIncomingFrame(frameBytes: ByteArray, frameSize: Int) {
                nativeQueueIncoming(frameBytes, frameSize)
            }

            override fun dequeueOutgoingFrame(outputBuffer: ByteArray): Int {
                return nativeGetEncodedFrame(outputBuffer)
            }
        }
        galTransport = ALTransport(frameParser)
        nativeInit()
        nativeSetPhoneInfo(phoneInfo)
    }
}