package com.link.riderservice.feature.connection.wifi

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.net.NetworkInfo
import android.net.wifi.WifiManager
import android.net.wifi.p2p.WifiP2pConfig
import android.net.wifi.p2p.WifiP2pDevice
import android.net.wifi.p2p.WifiP2pDeviceList
import android.net.wifi.p2p.WifiP2pManager
import android.net.wifi.p2p.WifiP2pManager.ActionListener
import android.net.wifi.p2p.WifiP2pManager.ChannelListener
import com.link.riderservice.api.SPRiderServices
import com.link.riderservice.core.extensions.coroutines.mainScope
import com.link.riderservice.core.utils.countDownByFlow
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.core.utils.logging.logW
import kotlinx.coroutines.Job
import java.util.concurrent.atomic.AtomicBoolean

internal class SoftP2pManager(
    val listener: SoftIP2pListener
) : ChannelListener,
    WifiP2pManager.PeerListListener {
    private val context = SPRiderServices.getSharedInstance().application.applicationContext
    private val manager: WifiP2pManager? by lazy(LazyThreadSafetyMode.NONE) {
        context.getSystemService(Context.WIFI_P2P_SERVICE) as WifiP2pManager?
    }

    private val wifiManager: WifiManager? by lazy(LazyThreadSafetyMode.NONE) {
        context.getSystemService(Context.WIFI_SERVICE) as WifiManager?
    }
    private var wifiP2pChannel: WifiP2pManager.Channel? =
        manager?.initialize(context, context.mainLooper, null)
    private var peerAddress: String = ""
    private var peerPort: Int = 0
    private var isConnecting = AtomicBoolean(false)
    private var retryChannel = false
    private var isWifiExist = false
    private var searchCountdownJob: Job? = null
    private var connectCountdownJob: Job? = null
    private var autolinkStation = AUTOLINK_STATION_WIFI_START
    private var isScanning = false
    private var isWifiConnected = false

    /** wifi连接重试次数*/
    private var wifiConnectionRetryCount = 0

    // 用于监听 WLAN 直连状态的广播
    private val broadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        @SuppressLint("MissingPermission")
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                WifiManager.WIFI_STATE_CHANGED_ACTION -> {
                    when (intent.getIntExtra(WifiManager.EXTRA_WIFI_STATE, 0)) {
                        WifiManager.WIFI_STATE_DISABLED -> {
                            isConnecting.set(false)
                            listener.onWifiState(false)
                            autolinkStation = AUTOLINK_STATION_WIFI_DISABLE
                        }

                        WifiManager.WIFI_STATE_ENABLING -> {
                            if (autolinkStation == AUTOLINK_STATION_WIFI_DISABLE) {
                                listener.requestWifiInfo()
                            }
                        }

                        WifiManager.WIFI_STATE_ENABLED -> {
                            listener.onWifiState(true)
                        }
                    }
                }

                WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION -> {
                    val discoveryState = intent.getIntExtra(
                        WifiP2pManager.EXTRA_DISCOVERY_STATE,
                        WifiP2pManager.WIFI_P2P_DISCOVERY_STOPPED
                    )
                    isScanning = discoveryState == WifiP2pManager.WIFI_P2P_DISCOVERY_STARTED
                    discoverPeers()
                }

                WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION -> {
                    if (intent.getIntExtra(
                            WifiP2pManager.EXTRA_WIFI_STATE,
                            WifiP2pManager.WIFI_P2P_STATE_DISABLED
                        ) == WifiP2pManager.WIFI_P2P_STATE_ENABLED
                    ) {
                        discoverPeers()
                    }
                }

                WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION -> {
                    manager?.requestPeers(wifiP2pChannel, this@SoftP2pManager)
                }

                WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION -> {
                    val networkInfo =
                        intent.getParcelableExtra<NetworkInfo>(WifiP2pManager.EXTRA_NETWORK_INFO)
                    logD(TAG, "P2P connection changed: ${networkInfo?.isConnected}")

                    if (networkInfo?.isConnected == true) {
                        if (autolinkStationChange(AUTOLINK_STATION_CONNECT_SUCCESS)) {
                            wifiConnectionRetryCount = 0
                            isWifiConnected = true
                            connectCountdownJob?.cancel()
                            listener.onWifiConnectSuccess()
                            stopSearch()
                        }
                    } else {
                        if (isWifiConnected) {
                            isWifiConnected = false
                            isConnecting.set(false)
                        }
                        listener.onWifiDisconnect()
                    }
                }

                WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION -> {
                    val device =
                        intent.getParcelableExtra<WifiP2pDevice>(WifiP2pManager.EXTRA_WIFI_P2P_DEVICE)
                    logD(TAG, "P2P device status: ${device?.status}")
                    if (device?.status == WifiP2pDevice.CONNECTED && !isConnecting.get()) {
                        logD(TAG, "P2P device connected: ${device.deviceName}")
                        disconnect()
                        listener.requestWifiInfo()
                    }
                }
            }
        }
    }

    /**
     * 初始化 WLAN 直连
     */
    @SuppressLint("MissingPermission")
    internal fun start(address: String, port: Int) {
        logD(TAG, "connect analysis:start:isScanning = $isScanning")
        if (isScanning) {
            stop()
        }
        logD(TAG, "p2p module start")
        isConnecting.set(false)
        peerAddress = address
        peerPort = port
        autolinkStationChange(AUTOLINK_STATION_WIFI_START)
        val wifiManager = context.getSystemService(Context.WIFI_SERVICE) as WifiManager
        if (!wifiManager.isWifiEnabled) {
            listener.onWifiState(false)
        }
        //判断是否已经连接
        manager?.requestConnectionInfo(wifiP2pChannel) {
            if (it.groupFormed) {
                if (autolinkStationChange(AUTOLINK_STATION_ALREADY_CONNECTED)) {
                    isWifiConnected = true
                    isConnecting.set(true)
                }
            } else {
                if (autolinkStationChange(AUTOLINK_STATION_NO_CONNECT)) {
                    wifiConnectionRetryCount = 0
                    searchExist()
                }
            }
        }
        //开启搜索
        discoverPeers()
    }

    internal fun stop() {
        logD(TAG, "p2p module stop")
        //停止搜索
        stopSearch()
        //断开连接
        disconnect()

        connectCountdownJob?.cancel()
        connectCountdownJob = null
        isWifiConnected = false
    }

    private fun initP2p(): Boolean {
        if (!context.packageManager.hasSystemFeature(PackageManager.FEATURE_WIFI_DIRECT)) {
            logE(TAG, "Wi-Fi Direct is not supported by this device.")
            return false
        }
        if (wifiManager == null) {
            logE(TAG, "Cannot get Wi-Fi system service.")
            return false
        }
        if (wifiManager?.isP2pSupported == false) {
            logE(TAG, "Wi-Fi Direct is not supported by the hardware or Wi-Fi is off.")
            return false
        }

        if (manager == null) {
            logE(TAG, "Cannot get Wi-Fi Direct system service.")
            return false
        }
        return true
    }

    @SuppressLint("MissingPermission")
    @Synchronized
    private fun connect(wifiP2pDevice: WifiP2pDevice) {
        wifiConnectionRetryCount++
        isWifiExist = true
        if (wifiP2pDevice.status == WifiP2pDevice.AVAILABLE && !isConnecting.get()) {
            logD(TAG, "connect wifi ${wifiP2pDevice.deviceName} ${wifiP2pDevice.deviceAddress}")
            isConnecting.set(true)
            val config = WifiP2pConfig()
            config.deviceAddress = wifiP2pDevice.deviceAddress
            manager?.connect(wifiP2pChannel, config, object : ActionListener {
                override fun onSuccess() {
                    listener.onP2pConnectSuccess()
                    startConnectCountDown()
                }

                override fun onFailure(reason: Int) {
                    logD(TAG, "Connect failed. Retry.")
                    isConnecting.set(false)
                }
            })
        }
    }


    private fun cancelConnect() {
        wifiP2pChannel?.run {
            manager?.cancelConnect(this, object : ActionListener {
                override fun onSuccess() {
                    logD(TAG, "cancelConnect")
                }

                override fun onFailure(reasonCode: Int) {
                    logE(TAG, "cancelConnect fail reason:$reasonCode")
                }

            })
        }
        listener.onCancelConnect()
    }

    private fun registerReceiver() {
        logD(TAG, "register wifi receiver")
        context.registerReceiver(broadcastReceiver, IntentFilter().apply {
            addAction(WifiManager.WIFI_STATE_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_DISCOVERY_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_STATE_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_PEERS_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_CONNECTION_CHANGED_ACTION)
            addAction(WifiP2pManager.WIFI_P2P_THIS_DEVICE_CHANGED_ACTION)
        })
    }

    private fun unregisterReceiver() {
        logD(TAG, "unregister wifi receiver")
        try {
            context.unregisterReceiver(broadcastReceiver)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @SuppressLint("MissingPermission")
    private fun discoverPeers() {
        if (isWifiConnected) {
            return
        }
        if (isScanning) {
            manager?.requestPeers(wifiP2pChannel, this@SoftP2pManager)
        } else {
            wifiP2pChannel?.let {
                manager?.discoverPeers(it, object : ActionListener {
                    override fun onSuccess() {
                        logD(TAG, "discoverPeers Success")
                    }

                    override fun onFailure(reasonCode: Int) {
                        logD(TAG, "discoverPeers failed: $reasonCode")
                    }
                })
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun stopSearch() {
        if (!isScanning) {
            return
        }
        manager?.stopPeerDiscovery(wifiP2pChannel, object : ActionListener {
            override fun onSuccess() {
                logD(TAG, "stopPeerDiscovery Success: ")
            }

            override fun onFailure(reasonCode: Int) {
                logE(TAG, "stopPeerDiscovery failed: $reasonCode")
            }
        })
    }

    private fun disconnect() {
        isWifiExist = false
        isConnecting.set(false)
        if (isWifiConnected) {
            wifiP2pChannel?.run {
                manager?.removeGroup(this, object : ActionListener {
                    override fun onSuccess() {
                        if (autolinkStationChange(AUTOLINK_STATION_NO_CONNECT)) {
                            wifiConnectionRetryCount = 0
                            searchExist()
                        }
                        logD(TAG, "disconnect success")
                    }

                    override fun onFailure(reasonCode: Int) {
                        logD(TAG, "disconnect failed. Reason :$reasonCode")
                    }
                })
            }
        }
        isWifiConnected = false
    }


    override fun onChannelDisconnected() {
        logW(TAG, "Channel lost")
        if (manager != null && !retryChannel) {
            retryChannel = true
            wifiP2pChannel = manager?.initialize(context, context.mainLooper, this)
        }
    }

    override fun onPeersAvailable(peers: WifiP2pDeviceList) {
        val foundDevices = peers.deviceList
        foundDevices?.run {
            find { it.isGroupOwner && it.status == WifiP2pDevice.CONNECTED }
                ?.let {
                    if (it.deviceAddress.lowercase() != peerAddress.lowercase()) {
                        logD(TAG, it.deviceAddress)
                        if (autolinkStationChange(AUTOLINK_STATION_P2P_DEVICE_ERROR)) {
                            disconnect()
                        }
                    }
                }

            find { it.deviceAddress.lowercase() == peerAddress.lowercase() }?.let {
                logD(TAG, "wifi p2p peer status:${it.status} ${isConnecting.get()}")
                if (isConnecting.get()) {
                    if (autolinkStationChange(AUTOLINK_STATION_AUTOLINK_RESTART)) {
                        if (it.isGroupOwner && it.status == WifiP2pDevice.CONNECTED) {
                            logD(TAG, "已经存在连接")
                            listener.connectExist()
                            isWifiConnected = true
                        } else {
                            logD(TAG, "平台端状态不正确")
                        }
                    }
                } else {
                    if (autolinkStationChange(AUTOLINK_STATION_SEARCH_SUCCESS)) {
                        connect(it)
                    }
                }
            }
        }
    }

    private fun startConnectCountDown() {
        connectCountdownJob = countDownByFlow(10, 1000, mainScope,
            onTick = {
            }, onFinish = {
                logD(TAG, "startConnectCountDown")
                if (!isWifiConnected) {
                    cancelConnect()
                }
            })
    }

    private fun searchExist() {
        if (autolinkStationChange(AUTOLINK_STATION_SEARCH_WIFI)) {
            searchCountdownJob = countDownByFlow(5, 1000, mainScope,
                onTick = {
                    logD(TAG, "search result:$isWifiExist")
                    if (isWifiExist) {
                        searchCountdownJob?.cancel()
                    }
                }, onFinish = {
                    //扫描10秒后没有找到wifi，请求重置平台端wifi
                    if (autolinkStationChange(AUTOLINK_STATION_SEARCH_FAILED)) {
                        logD(TAG, "search finish result:$isWifiExist")
                        if (!isWifiExist) {
                            listener.requestWifiInfo()
                        }
                    }
                })
        }
    }

    fun autolinkStationChange(newState: Int): Boolean {
        if (autolinkStation == AUTOLINK_STATION_WIFI_START
            && (newState == AUTOLINK_STATION_NO_CONNECT || newState == AUTOLINK_STATION_ALREADY_CONNECTED)
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_NO_CONNECT
            && newState == AUTOLINK_STATION_SEARCH_WIFI
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_ALREADY_CONNECTED
            && (newState == AUTOLINK_STATION_WIFI_START || newState == AUTOLINK_STATION_AUTOLINK_RESTART || newState == AUTOLINK_STATION_P2P_DEVICE_ERROR)
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_SEARCH_WIFI
            && (newState == AUTOLINK_STATION_SEARCH_FAILED || newState == AUTOLINK_STATION_SEARCH_SUCCESS)
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_SEARCH_FAILED
            && newState == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_SEARCH_SUCCESS
            && (newState == AUTOLINK_STATION_CONNECT_SUCCESS || newState == AUTOLINK_STATION_SEARCH_SUCCESS || newState == AUTOLINK_STATION_CONNECT_FAILED || newState == AUTOLINK_STATION_WIFI_START)
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_CONNECT_FAILED
            && (newState == AUTOLINK_STATION_CONNECT_SUCCESS || newState == AUTOLINK_STATION_RECONNECT_FAILED)
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_CONNECT_SUCCESS
            && newState == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_RECONNECT_FAILED
            && newState == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_AUTOLINK_RESTART
            && newState == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_P2P_DEVICE_ERROR
            && newState == AUTOLINK_STATION_NO_CONNECT
        ) {
            autolinkStation = newState
            return true
        }
        if (autolinkStation == AUTOLINK_STATION_WIFI_DISABLE
            && newState == AUTOLINK_STATION_WIFI_START
        ) {
            autolinkStation = newState
            return true
        }
        logE(TAG, " newState failed return false autolinkStation:$autolinkStation,newState:$newState")
        return false
    }

    init {
        if (wifiP2pChannel == null) {
            logE(TAG, "Cannot initialize Wi-Fi Direct.")
        }
        if (!initP2p()) {
            logE(TAG, "initP2p failed")
        }
        registerReceiver()
    }

    companion object {
        private const val TAG = "SoftP2pManager"

        /**进入app时，未获取到其他状态:0 */
        private const val AUTOLINK_STATION_WIFI_START = 0

        /**进入app时，wifi p2p未连接:1 */
        private const val AUTOLINK_STATION_NO_CONNECT = 1

        /**进入app时，wifi p2p已经连接:2*/
        private const val AUTOLINK_STATION_ALREADY_CONNECTED = 2

        /**搜索wifi，持续搜索10s:3*/
        private const val AUTOLINK_STATION_SEARCH_WIFI = 3

        /**搜索wifi重试失败，向平台请求重置:4*/
        private const val AUTOLINK_STATION_SEARCH_FAILED = 4

        /**wifi搜索成功:5*/
        private const val AUTOLINK_STATION_SEARCH_SUCCESS = 5

        /**wifi连接成功:6*/
        private const val AUTOLINK_STATION_CONNECT_SUCCESS = 6

        /**wifi连接失败，重试3次:7 */
        private const val AUTOLINK_STATION_CONNECT_FAILED = 7

        /**wifi连接重试失败，向平台请求重置:8*/
        private const val AUTOLINK_STATION_RECONNECT_FAILED = 8

        /**wifi已经连接，进入Autolink重启:9*/
        private const val AUTOLINK_STATION_AUTOLINK_RESTART = 9

        /**wifi p2p对等设备错误，移除设备:10*/
        private const val AUTOLINK_STATION_P2P_DEVICE_ERROR = 10

        /**wifi关闭:11*/
        private const val AUTOLINK_STATION_WIFI_DISABLE = 11
    }
}