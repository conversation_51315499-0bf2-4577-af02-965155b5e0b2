package com.link.riderservice.feature.display.video

import android.content.Context
import android.graphics.SurfaceTexture
import android.graphics.SurfaceTexture.OnFrameAvailableListener
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.media.MediaFormat.MIMETYPE_VIDEO_AVC
import android.media.projection.MediaProjection
import android.opengl.EGLContext
import android.opengl.GLES20
import android.os.Build
import android.os.Handler
import android.os.HandlerThread
import android.view.Display
import android.view.Surface
import com.link.riderservice.api.SPNaviMode
import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logE
import com.link.riderservice.core.utils.logging.logW
import com.link.riderservice.feature.display.config.VideoPackage
import com.link.riderservice.feature.display.protocol.source.ALVideoSource
import com.link.riderservice.libs.glutils.EglTask
import com.link.riderservice.libs.glutils.FullFrameRect
import com.link.riderservice.libs.glutils.Texture2dProgram
import com.link.riderservice.libs.glutils.WindowSurface
import java.lang.Integer.max
import java.lang.Integer.min
import kotlin.math.roundToInt

/**
 * 媒体投影服务
 * 负责屏幕录制、显示镜像和视频编码功能
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
internal class MediaProjectService(
    val context: Context,
    private val onDisplayInitialized: (display: Display) -> Unit,
    private val onDisplayReleased: (display: Display) -> Unit,
    private val onRequestMediaProjection: () -> Unit,
    private val onMirrorStart: () -> Unit,
    private val onMirrorStop: () -> Unit,
    private val onPresentationStart: () -> Unit = {},
    private val onPresentationStop: () -> Unit = {},
    private val onEncodedFrame: (pkg: VideoPackage) -> Unit
) : ALVideoSource.IVideoCallback {

    // ==================== 常量定义 ====================

    companion object {
        private const val TAG = "MediaProjectService"

        // 编码参数
        private const val BITRATE = 6 * 1024 * 1024 // 6Mbps
        private const val I_FRAME_INTERVAL = 1 // I帧间隔
        private const val DEFAULT_FRAME_RATE = 30 // 默认帧率
        private const val FRAME_INTERVAL_MILLIS = 1000L / DEFAULT_FRAME_RATE // 帧间隔毫秒

        // 密度DPI计算参数
        private const val BASE_DPI = 160.0f
        private const val DPI_MULTIPLIER = 1.6f
        private const val ASPECT_RATIO_THRESHOLD_MIN = 1.34f
        private const val ASPECT_RATIO_THRESHOLD_MAX = 2.0f

        // 预定义分辨率DPI映射
        private val RESOLUTION_DPI_MAP = mapOf(
            Pair(1024, 600) to 256,
            Pair(1024, 496) to 256,
            Pair(1280, 720) to 320,
            Pair(1920, 1080) to 480,
            Pair(800, 480) to 240
        )

        // 虚拟显示器名称
        private const val MIRROR_DISPLAY_NAME = "Mirror Display"
        private const val PRESENTATION_DISPLAY_NAME = "ext-display"

        // 线程名称
        private const val CODEC_THREAD_NAME = TAG
        private const val SCREEN_CAPTURE_THREAD_NAME = "ScreenCaptureThread"
    }

    // ==================== 成员变量 ====================

    // MediaCodec相关
    private var mediaCodec: MediaCodec? = null
    private var codecInputSurface: Surface? = null
    private val videoPackage = VideoPackage()

    // 线程处理
    private val codecHandlerThread: HandlerThread = HandlerThread(CODEC_THREAD_NAME)
    private lateinit var backgroundHandler: Handler

    // 显示相关
    private var presentationVirtualDisplay: VirtualDisplay? = null
    private var mediaProjection: MediaProjection? = null
    private val displayManager: DisplayManager =
        context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager

    // 配置参数
    private var navigationMode: SPNaviMode = SPNaviMode.SPNaviModeDefaultNavi
    private var displayWidth: Int = 0
    private var displayHeight: Int = 0

    // 渲染控制
    private var screenCaptureThread: Thread? = null
    private val synchronizationLock = Object()

    @Volatile
    private var isRecording = false

    @Volatile
    private var isDrawRequested = false

    @Volatile
    private var isEncodeRequested = false

    @Volatile
    private var isFirstFrame = true

    // MediaFormat配置
    private val mediaFormat = createMediaFormat()

    // ==================== 初始化 ====================

    init {
        initializeComponents()
    }

    /**
     * 初始化组件
     */
    private fun initializeComponents() {
        codecHandlerThread.start()
        backgroundHandler = Handler(codecHandlerThread.looper)
        logD(TAG, "MediaProjectService initialized")
    }

    /**
     * 创建MediaFormat配置
     */
    private fun createMediaFormat(): MediaFormat {
        return MediaFormat().apply {
            setString(MediaFormat.KEY_MIME, MIMETYPE_VIDEO_AVC)
            setInteger(
                MediaFormat.KEY_COLOR_FORMAT,
                MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface
            )
            setInteger(MediaFormat.KEY_BIT_RATE, BITRATE)
            setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, I_FRAME_INTERVAL)
        }
    }

    // ==================== 公共接口方法 ====================

    /**
     * 设置导航模式
     */
    fun setNaviMode(newNavigationMode: SPNaviMode) {
        logD(TAG, "setNaviMode $newNavigationMode")
        navigationMode = newNavigationMode
    }

    /**
     * 设置MediaProjection并开始渲染
     */
    fun setMediaProjection(newMediaProjection: MediaProjection) {
        <EMAIL> = newMediaProjection
        startRenderProcess()
    }

    // ==================== ALVideoSource.IVideoCallback 实现 ====================

    override fun onVideoConfig(codecType: Int, framesPerSecond: Int, width: Int, height: Int) {
        logD(TAG, "onVideoConfig: ${width}x${height}@${framesPerSecond}fps")

        updateDisplayDimensions(width, height)
        configureMediaFormat(framesPerSecond)
        initializeMediaCodec()

        if (navigationMode == SPNaviMode.SPNaviModeMirrorNavi) {
            initializeRealVirtualDisplay()
        } else {
            initializeVirtualDisplay()
        }
    }

    override fun onStart() {
        logD(TAG, "onStart $navigationMode")
        startMediaCodec()
        startRenderProcess()
    }

    override fun onStop() {
        logD(TAG, "onStop navigationMode=$navigationMode")
        stopMediaCodec()
        stopRenderProcess()
        releaseVirtualDisplay()
        handleModeSpecificStop()
    }

    override fun onShutdown() {
        logD(TAG, "onShutdown")
        onStop()
        releaseMediaProjection()
    }

    // ==================== 私有方法 - 配置管理 ====================

    /**
     * 更新显示尺寸
     */
    private fun updateDisplayDimensions(width: Int, height: Int) {
        displayWidth = width
        displayHeight = height
    }

    /**
     * 配置MediaFormat参数
     */
    private fun configureMediaFormat(framesPerSecond: Int) {
        mediaFormat.apply {
            setInteger(MediaFormat.KEY_WIDTH, displayWidth)
            setInteger(MediaFormat.KEY_HEIGHT, displayHeight)
            setInteger(MediaFormat.KEY_FRAME_RATE, framesPerSecond)
            setInteger(MediaFormat.KEY_CAPTURE_RATE, framesPerSecond)
            setInteger(MediaFormat.KEY_REPEAT_PREVIOUS_FRAME_AFTER, 1000000 / framesPerSecond)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                setInteger(MediaFormat.KEY_PROFILE, MediaCodecInfo.CodecProfileLevel.AVCProfileHigh)
                setInteger(MediaFormat.KEY_LEVEL, MediaCodecInfo.CodecProfileLevel.AVCLevel41)
            }
        }
    }

    // ==================== 私有方法 - MediaCodec管理 ====================

    /**
     * 初始化MediaCodec
     */
    private fun initializeMediaCodec() {
        try {
            if (mediaCodec == null) {
                mediaCodec = MediaCodec.createEncoderByType("video/avc")
            }
            mediaCodec?.apply {
                setCallback(createCodecCallback(), backgroundHandler)
                configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)
                codecInputSurface = createInputSurface()
            }
        } catch (exception: Exception) {
            logE(TAG, "Failed to initialize MediaCodec", exception)
            throw exception
        }
    }

    /**
     * 启动MediaCodec
     */
    private fun startMediaCodec() {
        try {
            mediaCodec?.start()
        } catch (exception: Exception) {
            logE(TAG, "Failed to start MediaCodec", exception)
        }
    }

    /**
     * 停止MediaCodec
     */
    private fun stopMediaCodec() {
        try {
            mediaCodec?.apply {
                stop()
                release()
            }
        } catch (exception: Exception) {
            logE(TAG, "Error stopping MediaCodec", exception)
        } finally {
            mediaCodec = null
            releaseCodecInputSurface()
        }
    }

    /**
     * 释放Codec输入Surface
     */
    private fun releaseCodecInputSurface() {
        codecInputSurface?.apply {
            try {
                release()
            } catch (exception: Exception) {
                logE(TAG, "Error releasing codec input surface", exception)
            }
        }
        codecInputSurface = null
    }

    /**
     * 创建MediaCodec回调
     */
    private fun createCodecCallback() = object : MediaCodec.Callback() {
        override fun onInputBufferAvailable(codecType: MediaCodec, bufferIndex: Int) {
            // 输入缓冲区可用，当前实现中不需要处理
        }

        override fun onOutputBufferAvailable(
            codecType: MediaCodec,
            bufferIndex: Int,
            bufferInfo: MediaCodec.BufferInfo
        ) {
            handleFirstFrameIfNeeded()
            processEncodedFrame(codecType, bufferIndex, bufferInfo)
        }

        override fun onError(codecType: MediaCodec, exception: MediaCodec.CodecException) {
            logE(TAG, "MediaCodec error", exception)
        }

        override fun onOutputFormatChanged(codecType: MediaCodec, format: MediaFormat) {
            logD(TAG, "MediaCodec output format changed: $format")
        }
    }

    /**
     * 处理首帧标记
     */
    private fun handleFirstFrameIfNeeded() {
        if (isFirstFrame) {
            isFirstFrame = false
        }
    }

    /**
     * 处理编码后的帧数据
     */
    private fun processEncodedFrame(
        codecType: MediaCodec,
        bufferIndex: Int,
        bufferInfo: MediaCodec.BufferInfo
    ) {
        try {
            codecType.getOutputBuffer(bufferIndex)?.let { outputBuffer ->
                outputBuffer.position(bufferInfo.offset)
                outputBuffer.limit(bufferInfo.offset + bufferInfo.size)

                videoPackage.apply {
                    data = ByteArray(bufferInfo.size)
                    size = bufferInfo.size
                    flags = bufferInfo.flags
                }

                outputBuffer.get(videoPackage.data, 0, bufferInfo.size)
                onEncodedFrame(videoPackage)
                codecType.releaseOutputBuffer(bufferIndex, false)
            }
        } catch (exception: IllegalStateException) {
            logE(TAG, "Error processing encoded frame", exception)
        }
    }

    // ==================== 私有方法 - 虚拟显示器管理 ====================

    /**
     * 初始化虚拟显示器（Presentation模式）
     */
    private fun initializeVirtualDisplay() {
        // 当前实现为空，保留原有逻辑
        logD(TAG, "initializeVirtualDisplay for presentation mode")
    }

    /**
     * 初始化真实虚拟显示器（Mirror模式）
     */
    private fun initializeRealVirtualDisplay() {
        if (mediaProjection == null) {
            logW(TAG, "MediaProjection is null, requesting projection")
            onRequestMediaProjection()
            return
        }
        logD(TAG, "initializeRealVirtualDisplay for mirror mode")
    }

    /**
     * 释放虚拟显示器
     */
    @Synchronized
    private fun releaseVirtualDisplay() {
        presentationVirtualDisplay?.let { virtualDisplay ->
            try {
                onDisplayReleased(virtualDisplay.display)
                virtualDisplay.release()
            } catch (exception: Exception) {
                logE(TAG, "Error releasing virtual display", exception)
            }
        }
        presentationVirtualDisplay = null
    }

    // ==================== 私有方法 - 渲染管理 ====================

    /**
     * 开始渲染进程
     */
    private fun startRenderProcess() {
        if (navigationMode == SPNaviMode.SPNaviModeMirrorNavi && mediaProjection == null) {
            logW(TAG, "startRender: mediaProjection is null")
            return
        }

        stopExistingRenderThread()
        startNewRenderThread()
    }

    /**
     * 停止现有渲染线程
     */
    private fun stopExistingRenderThread() {
        screenCaptureThread?.let { thread ->
            if (thread.isAlive) {
                try {
                    thread.join()
                } catch (exception: InterruptedException) {
                    logE(TAG, "Interrupted while waiting for render thread", exception)
                }
            }
        }
        screenCaptureThread = null
    }

    /**
     * 启动新的渲染线程
     */
    private fun startNewRenderThread() {
        isRecording = true
        val drawRunnable = DrawTask(null, 0)
        screenCaptureThread = Thread(drawRunnable, SCREEN_CAPTURE_THREAD_NAME).apply {
            start()
        }
    }

    /**
     * 停止渲染进程
     */
    private fun stopRenderProcess() {
        synchronized(synchronizationLock) {
            isRecording = false
            synchronizationLock.notifyAll()
        }

        screenCaptureThread?.let { thread ->
            try {
                thread.interrupt()
            } catch (exception: Exception) {
                logE(TAG, "Error interrupting render thread", exception)
            }
        }
    }

    // ==================== 私有方法 - 模式处理 ====================

    /**
     * 处理特定模式的停止逻辑
     */
    private fun handleModeSpecificStop() {
        when {
            navigationMode == SPNaviMode.SPNaviModeLockScreenNavi || navigationMode == SPNaviMode.SPNaviModeMirrorNavi -> {
                // 锁屏导航或镜像导航模式不需要特殊处理
                logD(TAG, "Skip mode-specific stop for $navigationMode")
            }

            mediaProjection != null -> {
                releaseMediaProjection()
            }

            else -> {
                onPresentationStop()
            }
        }
    }

    /**
     * 释放MediaProjection
     */
    @Synchronized
    private fun releaseMediaProjection() {
        mediaProjection?.let { projection ->
            try {
                projection.stop()
                onMirrorStop()
            } catch (exception: Exception) {
                logE(TAG, "Error stopping media projection", exception)
            }
        }
        mediaProjection = null
    }

    // ==================== 私有方法 - DPI计算 ====================

    /**
     * 计算密度DPI
     */
    private fun calculateDensityDpi(): Int {
        val maxDimension = max(displayWidth, displayHeight)
        val minDimension = min(displayWidth, displayHeight)

        // 检查预定义分辨率
        RESOLUTION_DPI_MAP[Pair(maxDimension, minDimension)]?.let { dpi ->
            return dpi
        }

        return calculateDpiByAspectRatio(maxDimension, minDimension)
    }

    /**
     * 根据宽高比计算DPI
     */
    private fun calculateDpiByAspectRatio(maxDimension: Int, minDimension: Int): Int {
        val aspectRatio = maxDimension / minDimension.toFloat()

        return when {
            aspectRatio < ASPECT_RATIO_THRESHOLD_MIN -> 256
            aspectRatio > ASPECT_RATIO_THRESHOLD_MAX ->
                (((minDimension * BASE_DPI) / 640.0f) * DPI_MULTIPLIER).roundToInt()

            else ->
                (((minDimension * BASE_DPI) / 720.0f) * DPI_MULTIPLIER).roundToInt()
        }
    }

    // ==================== 内部类 - DrawTask ====================

    /**
     * 绘制任务类
     * 负责OpenGL渲染和帧编码
     */
    private inner class DrawTask(eglSharedContext: EGLContext?, eglFlags: Int) :
        EglTask(eglSharedContext, eglFlags) {

        // 渲染相关变量
        private var frameIntervalMillis: Long = FRAME_INTERVAL_MILLIS
        private var textureId = 0
        private var sourceSurfaceTexture: SurfaceTexture? = null
        private var sourceSurface: Surface? = null
        private var encoderWindowSurface: WindowSurface? = null
        private var fullFrameRectDrawer: FullFrameRect? = null
        private val textureTransformMatrix = FloatArray(16)
        private var lastFrameTimestampMillis = System.currentTimeMillis()

        override fun onStart() {
            logD(TAG, "DrawTask onStart $navigationMode")

            try {
                initializeRenderingComponents()
                setupSurfaceTexture()
                createVirtualDisplayForMode()
                queueEvent(drawTaskRunnable)
            } catch (exception: Exception) {
                logE(TAG, "Error starting DrawTask", exception)
                releaseSelf()
            }
        }

        /**
         * 初始化渲染组件
         */
        private fun initializeRenderingComponents() {
            fullFrameRectDrawer = FullFrameRect(
                Texture2dProgram(Texture2dProgram.ProgramType.TEXTURE_EXT)
            )
            textureId = fullFrameRectDrawer!!.createTextureObject()
            encoderWindowSurface = WindowSurface(eglCore, codecInputSurface)
        }

        /**
         * 设置SurfaceTexture
         */
        private fun setupSurfaceTexture() {
            sourceSurfaceTexture = SurfaceTexture(textureId, false).apply {
                setDefaultBufferSize(displayWidth, displayHeight)
                setOnFrameAvailableListener(frameAvailableListener, backgroundHandler)
            }
            sourceSurface = Surface(sourceSurfaceTexture)
        }

        /**
         * 根据模式创建虚拟显示器
         */
        private fun createVirtualDisplayForMode() {
            val densityDpi = calculateDensityDpi()

            presentationVirtualDisplay = if (navigationMode == SPNaviMode.SPNaviModeMirrorNavi) {
                createMirrorVirtualDisplay(densityDpi)
            } else {
                createPresentationVirtualDisplay(densityDpi)
            }
        }

        private val mediaProjectionCallback = object : MediaProjection.Callback() {
            override fun onStop() {
                super.onStop()
                releaseVirtualDisplay()
                releaseMediaProjection()
            }
        }

        /**
         * 创建镜像虚拟显示器
         */
        private fun createMirrorVirtualDisplay(densityDpi: Int): VirtualDisplay? {
            mediaProjection?.registerCallback(mediaProjectionCallback, backgroundHandler)
            return mediaProjection?.createVirtualDisplay(
                MIRROR_DISPLAY_NAME,
                displayWidth,
                displayHeight,
                densityDpi,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_PUBLIC,
                sourceSurface,
                null,
                null
            ).also {
                onMirrorStart()
            }
        }

        /**
         * 创建演示虚拟显示器
         */
        private fun createPresentationVirtualDisplay(densityDpi: Int): VirtualDisplay? {
            return displayManager.createVirtualDisplay(
                PRESENTATION_DISPLAY_NAME,
                displayWidth,
                displayHeight,
                densityDpi,
                sourceSurface,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_PRESENTATION
            ).also { virtualDisplay ->
                virtualDisplay?.display?.let { display ->
                    onDisplayInitialized(display)
                }
                onPresentationStart()
            }
        }

        override fun onStop() {
            logD(TAG, "DrawTask onStop")
            releaseRenderingResources()
        }

        /**
         * 释放渲染资源
         */
        private fun releaseRenderingResources() {
            try {
                fullFrameRectDrawer?.release()
                sourceSurface?.release()
                sourceSurfaceTexture?.release()
                encoderWindowSurface?.release()

                makeCurrent()

                releaseVirtualDisplay()
            } catch (exception: Exception) {
                logE(TAG, "Error releasing rendering resources", exception)
            } finally {
                // 确保所有引用都被清空
                fullFrameRectDrawer = null
                sourceSurface = null
                sourceSurfaceTexture = null
                encoderWindowSurface = null
                presentationVirtualDisplay = null
            }
        }

        override fun onError(exception: java.lang.Exception?): Boolean {
            logE(TAG, "DrawTask error: $exception")
            return true
        }

        override fun processRequest(requestCode: Int, argument1: Int, argument2: Any?): Boolean {
            return false
        }

        /**
         * 帧可用监听器
         */
        private val frameAvailableListener = OnFrameAvailableListener { _: SurfaceTexture? ->
            if (isRecording) {
                synchronized(synchronizationLock) {
                    isDrawRequested = true
                    synchronizationLock.notifyAll()
                }
            }
        }

        /**
         * 绘制任务Runnable
         */
        private val drawTaskRunnable: Runnable = object : Runnable {
            override fun run() {
                try {
                    val shouldDrawFrame = waitForDrawRequest()

                    if (isRecording) {
                        processFrame(shouldDrawFrame)
                        queueEvent(this)
                    } else {
                        releaseSelf()
                    }
                } catch (exception: Exception) {
                    logE(TAG, "Error in draw task", exception)
                    releaseSelf()
                }
            }

            /**
             * 等待绘制请求
             */
            private fun waitForDrawRequest(): Boolean {
                var shouldDrawFrame: Boolean
                synchronized(synchronizationLock) {
                    shouldDrawFrame = isDrawRequested
                    isDrawRequested = false

                    if (!shouldDrawFrame) {
                        try {
                            synchronizationLock.wait(frameIntervalMillis)
                            shouldDrawFrame = isDrawRequested
                            isDrawRequested = false
                        } catch (exception: InterruptedException) {
                            logD(TAG, "Draw task interrupted")
                            releaseSelf()
                            return false
                        }
                    }
                }
                return shouldDrawFrame
            }

            /**
             * 处理帧数据
             */
            private fun processFrame(shouldDrawFrame: Boolean) {
                if (shouldDrawFrame) {
                    updateTextureImage()
                    isEncodeRequested = true
                }

                encodeFrameIfNeeded()
                clearGLBuffer(shouldDrawFrame)
            }

            /**
             * 更新纹理图像
             */
            private fun updateTextureImage() {
                sourceSurfaceTexture?.apply {
                    updateTexImage()
                    getTransformMatrix(textureTransformMatrix)
                }
            }

            /**
             * 根据需要编码帧
             */
            private fun encodeFrameIfNeeded() {
                val currentTimeMillis = System.currentTimeMillis()
                if (currentTimeMillis - lastFrameTimestampMillis > frameIntervalMillis) {
                    lastFrameTimestampMillis = currentTimeMillis

                    if (isEncodeRequested) {
                        isEncodeRequested = false
                        performFrameEncoding()
                    }
                }
            }

            /**
             * 执行帧编码
             */
            private fun performFrameEncoding() {
                encoderWindowSurface?.apply {
                    makeCurrent()
                    fullFrameRectDrawer?.drawFrame(textureId, textureTransformMatrix)
                    swapBuffers()
                }
            }

            /**
             * 清除GL缓冲区
             */
            private fun clearGLBuffer(shouldDrawFrame: Boolean) {
                makeCurrent()
                if (shouldDrawFrame) {
                    GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT)
                    GLES20.glFlush()
                }
            }
        }
    }
}