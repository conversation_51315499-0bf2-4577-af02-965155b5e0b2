package com.link.riderservice.feature.analytics

import com.link.riderservice.core.utils.logging.logD
import com.link.riderservice.core.utils.logging.logI
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap

/**
 * 简化的连接时间记录器
 * 只记录关键的连接阶段时间
 */
object ConnectionAnalytics {

    private const val TAG = "CONNECTION_ANALYTICS"

    /** 当前活跃的连接记录 */
    private val activeConnections = ConcurrentHashMap<String, ConnectionTimeRecord>()

    /** 临时时间戳记录 */
    private val timeStamps = ConcurrentHashMap<String, Long>()

    // ==================== 主要API ====================

    /**
     * 开始记录连接时间
     * @param deviceId 设备ID
     */
    fun startConnection(deviceId: String) {
        // 如果已有活跃连接，先结束它
        if (activeConnections.isNotEmpty()) {
            finishConnection(false, "New connection started, previous connection abandoned")
        }

        val sessionId = generateSessionId()
        val record = ConnectionTimeRecord(
            sessionId = sessionId,
            deviceId = deviceId,
            startTime = System.currentTimeMillis()
        )

        activeConnections[sessionId] = record
        logD(TAG, "开始连接记录: Device=$deviceId, Session=$sessionId")
    }

    /**
     * 记录蓝牙扫描开始
     */
    fun startBluetoothScan() {
        val sessionId = getCurrentSessionId() ?: return
        timeStamps["${sessionId}_bt_scan_start"] = System.currentTimeMillis()
        logD(TAG, "蓝牙扫描开始")
    }

    /**
     * 记录蓝牙扫描结束
     */
    fun endBluetoothScan() {
        val sessionId = getCurrentSessionId() ?: return
        val startTime = timeStamps.remove("${sessionId}_bt_scan_start")
        if (startTime != null) {
            val duration = System.currentTimeMillis() - startTime
            activeConnections[sessionId]?.bluetoothScanTime = duration
            logD(TAG, "蓝牙扫描完成, 耗时: ${duration}ms")
        }
    }

    /**
     * 记录蓝牙连接开始
     */
    fun startBluetoothConnect() {
        val sessionId = getCurrentSessionId() ?: return
        timeStamps["${sessionId}_bt_connect_start"] = System.currentTimeMillis()
        logD(TAG, "蓝牙连接开始")
    }

    /**
     * 记录蓝牙连接结束
     */
    fun endBluetoothConnect() {
        val sessionId = getCurrentSessionId() ?: return
        val startTime = timeStamps.remove("${sessionId}_bt_connect_start")
        if (startTime != null) {
            val duration = System.currentTimeMillis() - startTime
            activeConnections[sessionId]?.bluetoothConnectTime = duration
            logD(TAG, "蓝牙连接完成, 耗时: ${duration}ms")
        }
    }

    /**
     * 记录WiFi扫描开始
     */
    fun startWifiScan() {
        val sessionId = getCurrentSessionId() ?: return
        timeStamps["${sessionId}_wifi_scan_start"] = System.currentTimeMillis()
        logD(TAG, "WiFi扫描开始")
    }

    /**
     * 记录WiFi扫描结束
     */
    fun endWifiScan() {
        val sessionId = getCurrentSessionId() ?: return
        val startTime = timeStamps.remove("${sessionId}_wifi_scan_start")
        if (startTime != null) {
            val duration = System.currentTimeMillis() - startTime
            activeConnections[sessionId]?.wifiScanTime = duration
            logD(TAG, "WiFi扫描完成, 耗时: ${duration}ms")
        }
    }

    /**
     * 记录WiFi连接开始
     */
    fun startWifiConnect() {
        val sessionId = getCurrentSessionId() ?: return
        timeStamps["${sessionId}_wifi_connect_start"] = System.currentTimeMillis()
        logD(TAG, "WiFi连接开始")
    }

    /**
     * 记录WiFi连接结束
     */
    fun endWifiConnect() {
        val sessionId = getCurrentSessionId() ?: return
        val startTime = timeStamps.remove("${sessionId}_wifi_connect_start")
        if (startTime != null) {
            val duration = System.currentTimeMillis() - startTime
            activeConnections[sessionId]?.wifiConnectTime = duration
            logD(TAG, "WiFi连接完成, 耗时: ${duration}ms")
        }
    }

    /**
     * 记录Socket连接开始
     */
    fun startSocketConnect() {
        val sessionId = getCurrentSessionId() ?: return
        timeStamps["${sessionId}_socket_connect_start"] = System.currentTimeMillis()
        logD(TAG, "Socket连接开始")
    }

    /**
     * 记录Socket连接结束
     */
    fun endSocketConnect() {
        val sessionId = getCurrentSessionId() ?: return
        val startTime = timeStamps.remove("${sessionId}_socket_connect_start")
        if (startTime != null) {
            val duration = System.currentTimeMillis() - startTime
            activeConnections[sessionId]?.socketConnectTime = duration
            logD(TAG, "Socket连接完成, 耗时: ${duration}ms")
        }
    }

    /**
     * 完成连接记录
     * @param isSuccess 是否成功
     * @param errorMessage 错误信息（如果失败）
     */
    fun finishConnection(isSuccess: Boolean, errorMessage: String? = null) {
        val sessionId = getCurrentSessionId() ?: return
        activeConnections[sessionId]?.let { record ->
            record.totalTime = record.getTotalDuration()
            record.isSuccess = isSuccess
            record.errorMessage = errorMessage

            // 输出完整记录
            logI(TAG, "连接完成: $record")

            // 记录到文件（仅Debug模式）
            SimpleConnectionLogger.logConnectionTime(record)

            // 清理临时数据
            activeConnections.remove(sessionId)
            cleanupTimeStamps(sessionId)
        }
    }

    /**
     * 获取当前活跃连接数量
     */
    fun getActiveConnectionCount(): Int = activeConnections.size

    /**
     * 获取当前活跃的会话ID
     */
    private fun getCurrentSessionId(): String? {
        return activeConnections.keys.firstOrNull()
    }

    // ==================== 内部方法 ====================

    /**
     * 生成会话ID
     */
    private fun generateSessionId(): String {
        return UUID.randomUUID().toString().substring(0, 8)
    }

    /**
     * 清理指定会话的临时时间戳
     */
    private fun cleanupTimeStamps(sessionId: String) {
        val keysToRemove = timeStamps.keys.filter { it.startsWith(sessionId) }
        keysToRemove.forEach { timeStamps.remove(it) }
    }
}
