import org.jetbrains.dokka.DokkaConfiguration.Visibility
import org.jetbrains.dokka.gradle.DokkaTask
import org.jetbrains.dokka.gradle.DokkaTaskPartial

plugins {
    id("com.android.library")
    id("org.jetbrains.kotlin.android")
    id("com.google.protobuf")
    id("org.jetbrains.dokka")
}

fun com.android.build.api.dsl.AndroidSourceSet.proto(action: SourceDirectorySet.() -> Unit) {
    (this as? ExtensionAware)
        ?.extensions
        ?.getByName("proto")
        ?.let { it as? SourceDirectorySet }
        ?.apply(action)
}

android {
    namespace = "com.link.riderservice"
    compileSdk = 35

    defaultConfig {
        minSdk = 24

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        externalNativeBuild {
            cmake {
                cFlags("-fvisibility=hidden", "-ffunction-sections", "-fdata-sections")
                cppFlags("-fvisibility=hidden", "-ffunction-sections", "-fdata-sections -std=c++11")
                abiFilters += listOf("armeabi-v7a", "arm64-v8a")
            }
        }

        ndk {
            debugSymbolLevel = "SYMBOL_TABLE"
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    externalNativeBuild {
        cmake {
            version = "3.22.1"
            path("src/main/cpp/CMakeLists.txt")
        }
    }

    ndkVersion = "27.2.12479018"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }

    sourceSets {
        getByName("main"){
            proto {
                srcDir("src/main/proto")
            }
        }
    }

    packaging {
        resources {
            excludes += "**/*.proto"
        }
    }

    libraryVariants.all {
        outputs.all {
            packageLibraryProvider {
                archiveFileName.set("${project.name}_1.0.0.aar")
            }
        }
    }

    buildFeatures {
        buildConfig = true
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:2.6.1"
    }
    generateProtoTasks {
        all().forEach {
            it.builtins {
                create("java")
            }
        }
    }
}

// 创建一个配置来收集要嵌入的JAR
val embedJars: Configuration by configurations.creating

dependencies {
    implementation(libs.androidx.core.ktx)
    
    // 将protobuf-java标记为embed，这样可以编译但不会传递依赖
    embedJars(libs.protobuf.java)
    compileOnly(libs.protobuf.java)
    
    compileOnly(libs.protoc)
    implementation(libs.kotlinx.coroutines.android)

    testImplementation(libs.junit)
    testImplementation(libs.protobuf.java) // 测试时需要实际的protobuf依赖
    androidTestImplementation(libs.androidx.test.ext)
    androidTestImplementation(libs.espresso.core)
}

// 创建Fat AAR任务
tasks.register("fatAar") {
    dependsOn("bundleReleaseAar")
    
    doLast {
        val originalAar = file("$buildDir/outputs/aar/${project.name}_1.0.0.aar")
        val fatAar = file("$buildDir/outputs/aar/${project.name}-fat_1.0.0.aar")
        val tempDir = file("$buildDir/tmp/fataar")
        val classesDir = file("$buildDir/tmp/classes")
        
        // 清理临时目录
        tempDir.deleteRecursively()
        classesDir.deleteRecursively()
        tempDir.mkdirs()
        classesDir.mkdirs()
        
        println("Creating fat AAR with embedded protobuf-java...")
        
        // 解压原始AAR
        copy {
            from(zipTree(originalAar))
            into(tempDir)
        }
        
        // 解压原始classes.jar
        val originalClassesJar = file("$tempDir/classes.jar")
        copy {
            from(zipTree(originalClassesJar))
            into(classesDir)
        }
        
        // 添加嵌入JAR的内容
        embedJars.forEach { jar ->
            println("Embedding JAR: ${jar.name}")
            copy {
                from(zipTree(jar)) {
                    exclude("META-INF/**")
                }
                into(classesDir)
            }
        }
        
        // 重新创建classes.jar
        ant.withGroovyBuilder {
            "jar"("destfile" to originalClassesJar.absolutePath, 
                  "basedir" to classesDir.absolutePath)
        }
        
        // 创建新的AAR
        ant.withGroovyBuilder {
            "zip"("destfile" to fatAar.absolutePath,
                  "basedir" to tempDir.absolutePath)
        }
        
        println("Fat AAR created: ${fatAar.absolutePath}")
        println("Original size: ${originalAar.length() / 1024}KB")
        println("Fat AAR size: ${fatAar.length() / 1024}KB")
    }
}

// 验证Fat AAR包含protobuf类
tasks.register("validateFatAar") {
    dependsOn("fatAar")
    
    doLast {
        val fatAar = file("$buildDir/outputs/aar/${project.name}-fat_1.0.0.aar")
        
        if (!fatAar.exists()) {
            throw GradleException("Fat AAR not found: ${fatAar.absolutePath}")
        }
        
        println("Validating Fat AAR: ${fatAar.absolutePath}")
        
        // 检查AAR内容
        val tempDir = file("$buildDir/tmp/validate")
        tempDir.deleteRecursively()
        tempDir.mkdirs()
        
        // 解压AAR
        copy {
            from(zipTree(fatAar))
            into(tempDir)
        }
        
        // 检查classes.jar
        val classesJar = file("$tempDir/classes.jar")
        if (!classesJar.exists()) {
            throw GradleException("classes.jar not found in AAR")
        }
        
        // 解压classes.jar并检查protobuf类
        val classesDir = file("$buildDir/tmp/validate-classes")
        classesDir.deleteRecursively()
        classesDir.mkdirs()
        
        copy {
            from(zipTree(classesJar))
            into(classesDir)
        }
        
        // 检查关键的protobuf类
        val protobufClasses = listOf(
            "com/google/protobuf/MessageLite.class",
            "com/google/protobuf/ByteString.class", 
            "com/google/protobuf/GeneratedMessage.class"
        )
        
        val missingClasses = mutableListOf<String>()
        protobufClasses.forEach { className ->
            val classFile = file("$classesDir/$className")
            if (!classFile.exists()) {
                missingClasses.add(className)
            } else {
                println("✓ Found: $className")
            }
        }
        
        if (missingClasses.isNotEmpty()) {
            throw GradleException("Missing protobuf classes: $missingClasses")
        }
        
        println("✓ Fat AAR validation successful!")
        println("  - Size: ${fatAar.length() / 1024}KB")
        println("  - Contains all required protobuf classes")
    }
}

tasks.withType<DokkaTask>().configureEach {
    dokkaSourceSets {
        named("main") {
            moduleName.set("SPRiderServices")
            noStdlibLink.set(false)
            noJdkLink.set(false)
            noAndroidSdkLink.set(false)

            documentedVisibilities.set(
                setOf(
                    Visibility.PUBLIC,
                    Visibility.PROTECTED
                )
            )

            perPackageOption {
                matchingRegex.set("(.*?)")
                suppress.set(true)
            }

            perPackageOption {
                matchingRegex.set("com.link.riderservice.api.*")
                suppress.set(false)
            }
        }
    }
}

tasks.register<Jar>("dokkaHtmlJar") {
    dependsOn(tasks.dokkaHtml)
    from(tasks.dokkaHtml.flatMap { it.outputDirectory })
    archiveClassifier.set("html-docs")
}

tasks.register<Jar>("dokkaJavadocJar") {
    dependsOn(tasks.dokkaJavadoc)
    from(tasks.dokkaJavadoc.flatMap { it.outputDirectory })
    archiveClassifier.set("javadoc")
}